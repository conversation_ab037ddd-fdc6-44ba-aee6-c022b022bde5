// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:event_vendor_app/main.dart';

void main() {
  testWidgets('Event Vendor App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const EventVendorApp());

    // Verify that the app loads without crashing
    await tester.pumpAndSettle();

    // This is a basic smoke test to ensure the app can be instantiated
    expect(find.byType(EventVendorApp), findsOneWidget);
  });
}
