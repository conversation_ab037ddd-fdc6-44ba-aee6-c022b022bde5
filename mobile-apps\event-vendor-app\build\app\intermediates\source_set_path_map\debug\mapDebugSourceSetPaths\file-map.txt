com.example.event_vendor_app-jetified-ads-adservices-java-1.1.0-beta11-0 C:\Users\<USER>\.gradle\caches\transforms-3\003e4d65ab76cd034bf15c41a512368d\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.event_vendor_app-jetified-startup-runtime-1.1.1-1 C:\Users\<USER>\.gradle\caches\transforms-3\03055962d1ced4f2adf1b4d7e3135faf\transformed\jetified-startup-runtime-1.1.1\res
com.example.event_vendor_app-slidingpanelayout-1.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\040a98d22271538076c900eb30290f7b\transformed\slidingpanelayout-1.2.0\res
com.example.event_vendor_app-appcompat-1.1.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\144b448b0ead06b1dcbd89cecb1fc0cf\transformed\appcompat-1.1.0\res
com.example.event_vendor_app-transition-1.4.1-4 C:\Users\<USER>\.gradle\caches\transforms-3\187c0a6dcd9ff886bcd2572d24e834ac\transformed\transition-1.4.1\res
com.example.event_vendor_app-jetified-savedstate-1.2.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\1e45c049671858c78a21c8e12970778f\transformed\jetified-savedstate-1.2.1\res
com.example.event_vendor_app-preference-1.2.1-6 C:\Users\<USER>\.gradle\caches\transforms-3\239439f9b02f7f3753373bb4fa3e60b0\transformed\preference-1.2.1\res
com.example.event_vendor_app-jetified-activity-ktx-1.10.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\24a84964d3479a28cb7231863820bf7f\transformed\jetified-activity-ktx-1.10.1\res
com.example.event_vendor_app-jetified-lifecycle-process-2.7.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\res
com.example.event_vendor_app-localbroadcastmanager-1.1.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\30909ea1cd339468227f0485ce0904ec\transformed\localbroadcastmanager-1.1.0\res
com.example.event_vendor_app-browser-1.8.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\37be4d6ba90a05589f451f5f9eb01978\transformed\browser-1.8.0\res
com.example.event_vendor_app-jetified-datastore-preferences-release-11 C:\Users\<USER>\.gradle\caches\transforms-3\37c65fbd7462c09f5b9aa72f8f5a1842\transformed\jetified-datastore-preferences-release\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\3a3911ada0e79dbd8642ae9bd5d24c03\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.event_vendor_app-core-runtime-2.2.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\405fc945f2d1ae97c6d54e9725d268bb\transformed\core-runtime-2.2.0\res
com.example.event_vendor_app-core-1.13.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\res
com.example.event_vendor_app-jetified-lifecycle-livedata-core-ktx-2.7.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\4dff4d4b9774e782264f6b79ee697671\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.event_vendor_app-recyclerview-1.0.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\53959f3933d0bee93fc29f16eff8d339\transformed\recyclerview-1.0.0\res
com.example.event_vendor_app-jetified-datastore-release-17 C:\Users\<USER>\.gradle\caches\transforms-3\61c673a7a54865c4cb87f410959a293a\transformed\jetified-datastore-release\res
com.example.event_vendor_app-jetified-profileinstaller-1.4.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\res
com.example.event_vendor_app-jetified-datastore-core-release-19 C:\Users\<USER>\.gradle\caches\transforms-3\717a9ee62906a036634cbddc9f5baac4\transformed\jetified-datastore-core-release\res
com.example.event_vendor_app-jetified-play-services-basement-18.5.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\72243b3865f1bb5f8322680f1038a2ab\transformed\jetified-play-services-basement-18.5.0\res
com.example.event_vendor_app-jetified-core-common-2.0.3-21 C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\res
com.example.event_vendor_app-coordinatorlayout-1.0.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\77fe8808db8b8e56715a835f9723ba6e\transformed\coordinatorlayout-1.0.0\res
com.example.event_vendor_app-jetified-credentials-play-services-auth-1.2.0-rc01-23 C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.event_vendor_app-jetified-ads-adservices-1.1.0-beta11-24 C:\Users\<USER>\.gradle\caches\transforms-3\7bb4227349f335858bec6b2869fd0c97\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.event_vendor_app-lifecycle-viewmodel-2.7.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\7fdb1870cd753cb43612bbcae7f812e5\transformed\lifecycle-viewmodel-2.7.0\res
com.example.event_vendor_app-jetified-firebase-common-21.0.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\res
com.example.event_vendor_app-jetified-tracing-1.2.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\8b483c6db5a73c4c4620d78e0cf1e3e7\transformed\jetified-tracing-1.2.0\res
com.example.event_vendor_app-jetified-savedstate-ktx-1.2.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\8f91ac1923a2613d0f2eacd0b1fa81ea\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.event_vendor_app-jetified-core-1.0.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\90ffc5c8d223f5f7810abc2b6e923a5e\transformed\jetified-core-1.0.0\res
com.example.event_vendor_app-jetified-fragment-ktx-1.7.1-30 C:\Users\<USER>\.gradle\caches\transforms-3\968dde55cdbb8b08b4c16fd88f0bcd88\transformed\jetified-fragment-ktx-1.7.1\res
com.example.event_vendor_app-jetified-core-viewtree-1.0.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\9a4ffb5e250252bbcd3c5d1b53e5650e\transformed\jetified-core-viewtree-1.0.0\res
com.example.event_vendor_app-jetified-annotation-experimental-1.4.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\9f03088b2d914b9554802ad1b4e6d9ad\transformed\jetified-annotation-experimental-1.4.0\res
com.example.event_vendor_app-jetified-firebase-messaging-24.1.2-33 C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\res
com.example.event_vendor_app-jetified-activity-1.10.1-34 C:\Users\<USER>\.gradle\caches\transforms-3\b8c73767230eb231b82628b2ed98f618\transformed\jetified-activity-1.10.1\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-ktx-2.7.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\bd43ea4ca0bfb02b46dee30a193a8751\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.event_vendor_app-jetified-core-ktx-1.13.1-36 C:\Users\<USER>\.gradle\caches\transforms-3\bda226ec0edc4bc1e35875b850b255a9\transformed\jetified-core-ktx-1.13.1\res
com.example.event_vendor_app-jetified-play-services-auth-21.0.0-37 C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\res
com.example.event_vendor_app-jetified-lifecycle-runtime-ktx-2.7.0-38 C:\Users\<USER>\.gradle\caches\transforms-3\cad94b76c8264dbd8e34f2692148e1bb\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.event_vendor_app-jetified-window-java-1.2.0-39 C:\Users\<USER>\.gradle\caches\transforms-3\d0bb67d09d858c737b179b016539f93f\transformed\jetified-window-java-1.2.0\res
com.example.event_vendor_app-lifecycle-runtime-2.7.0-40 C:\Users\<USER>\.gradle\caches\transforms-3\d2de2f3820f87f0e75f2b1dc9665f2fc\transformed\lifecycle-runtime-2.7.0\res
com.example.event_vendor_app-jetified-play-services-base-18.5.0-41 C:\Users\<USER>\.gradle\caches\transforms-3\d471c06edec8a15dc970c0328249b4a2\transformed\jetified-play-services-base-18.5.0\res
com.example.event_vendor_app-jetified-window-1.2.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\res
com.example.event_vendor_app-jetified-appcompat-resources-1.1.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\ec1ecd5ab7d976d0ee8c5467a1574419\transformed\jetified-appcompat-resources-1.1.0\res
com.example.event_vendor_app-lifecycle-livedata-core-2.7.0-44 C:\Users\<USER>\.gradle\caches\transforms-3\f4278ed60c184601f788abb865ebb729\transformed\lifecycle-livedata-core-2.7.0\res
com.example.event_vendor_app-jetified-credentials-1.2.0-rc01-45 C:\Users\<USER>\.gradle\caches\transforms-3\feb00cf3fff92e771f0faff0c647dc68\transformed\jetified-credentials-1.2.0-rc01\res
com.example.event_vendor_app-fragment-1.7.1-46 C:\Users\<USER>\.gradle\caches\transforms-3\ff362132f28c1d439476f1fa3a2fdfab\transformed\fragment-1.7.1\res
com.example.event_vendor_app-debug-47 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\debug\res
com.example.event_vendor_app-main-48 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\res
com.example.event_vendor_app-pngs-49 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\pngs\debug
com.example.event_vendor_app-resValues-50 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\resValues\debug
com.example.event_vendor_app-packageDebugResources-51 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.event_vendor_app-packageDebugResources-52 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.event_vendor_app-debug-53 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.event_vendor_app-debug-54 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-55 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-56 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-57 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-58 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-59 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-60 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-61 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-62 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-63 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geocoding_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-64 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-65 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-66 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-67 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-68 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-69 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-70 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-71 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-72 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
