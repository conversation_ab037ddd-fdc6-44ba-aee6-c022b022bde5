com.example.event_vendor_app-jetified-core-1.0.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\019bb53a73571693e528cbc2fbf11517\transformed\jetified-core-1.0.0\res
com.example.event_vendor_app-jetified-lifecycle-runtime-ktx-2.7.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0233e6e760e913790e05750c07058fa1\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.event_vendor_app-jetified-credentials-play-services-auth-1.2.0-rc01-2 C:\Users\<USER>\.gradle\caches\transforms-3\0290aaab56035474dc93869f3b5bf01c\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.event_vendor_app-jetified-appcompat-resources-1.1.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\048d63f2815195511c50e00fb12469d6\transformed\jetified-appcompat-resources-1.1.0\res
com.example.event_vendor_app-jetified-ads-adservices-1.1.0-beta11-4 C:\Users\<USER>\.gradle\caches\transforms-3\053ebb7dbb90f527d64ec234b0b94543\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.event_vendor_app-jetified-ads-adservices-java-1.1.0-beta11-5 C:\Users\<USER>\.gradle\caches\transforms-3\15188c866e4ff5e35c65edc054363a07\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.event_vendor_app-jetified-play-services-auth-21.0.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\154f02272795912e7155c617bb325ca1\transformed\jetified-play-services-auth-21.0.0\res
com.example.event_vendor_app-jetified-core-ktx-1.13.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\1bf1154445aac6989a03ca2e43e003e5\transformed\jetified-core-ktx-1.13.1\res
com.example.event_vendor_app-jetified-profileinstaller-1.4.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\31ac1ce820484ece1d02636ac62f988e\transformed\jetified-profileinstaller-1.4.0\res
com.example.event_vendor_app-jetified-activity-1.10.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\335c87e37d5f76be4d8c366f023c01e0\transformed\jetified-activity-1.10.1\res
com.example.event_vendor_app-jetified-startup-runtime-1.1.1-10 C:\Users\<USER>\.gradle\caches\transforms-3\34bc28d8be94b86ee5f74414228109cb\transformed\jetified-startup-runtime-1.1.1\res
com.example.event_vendor_app-jetified-annotation-experimental-1.4.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\39af03cbcd2af70933e13e73128a8c04\transformed\jetified-annotation-experimental-1.4.0\res
com.example.event_vendor_app-jetified-window-1.2.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\3aac23582bac27d9b74d172d5afaf272\transformed\jetified-window-1.2.0\res
com.example.event_vendor_app-jetified-savedstate-1.2.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\4fe6c5523c9733b800eb6ce889d7702a\transformed\jetified-savedstate-1.2.1\res
com.example.event_vendor_app-jetified-tracing-1.2.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\52ac8cf0fb9a04814b7549040bcc6f15\transformed\jetified-tracing-1.2.0\res
com.example.event_vendor_app-jetified-datastore-preferences-release-15 C:\Users\<USER>\.gradle\caches\transforms-3\554c69072caa1142f2a0636f27c9da1b\transformed\jetified-datastore-preferences-release\res
com.example.event_vendor_app-jetified-datastore-release-16 C:\Users\<USER>\.gradle\caches\transforms-3\5555962d6553634933abc535e9372275\transformed\jetified-datastore-release\res
com.example.event_vendor_app-appcompat-1.1.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\591da1a48a958d442215c4093bb458e6\transformed\appcompat-1.1.0\res
com.example.event_vendor_app-jetified-window-java-1.2.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\5adf42949914dc1bd475ad357218f7a8\transformed\jetified-window-java-1.2.0\res
com.example.event_vendor_app-jetified-fragment-ktx-1.7.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\71e5cb27519971f29160bfdcf86a07a1\transformed\jetified-fragment-ktx-1.7.1\res
com.example.event_vendor_app-jetified-credentials-1.2.0-rc01-20 C:\Users\<USER>\.gradle\caches\transforms-3\77b0af5e1299779a3a9698a95990dce8\transformed\jetified-credentials-1.2.0-rc01\res
com.example.event_vendor_app-jetified-lifecycle-process-2.7.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\787b3fc69bffdefc5ec60a3ee37a7f8d\transformed\jetified-lifecycle-process-2.7.0\res
com.example.event_vendor_app-jetified-play-services-base-18.5.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\7b5eb4bf95253b88a2aad39e0426e67c\transformed\jetified-play-services-base-18.5.0\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\829a87bda15e9b7e40edf83bab869192\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.event_vendor_app-jetified-firebase-messaging-24.1.2-24 C:\Users\<USER>\.gradle\caches\transforms-3\8642c542a593364818c455b3d8eddcfb\transformed\jetified-firebase-messaging-24.1.2\res
com.example.event_vendor_app-jetified-core-viewtree-1.0.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\8ca40b4206ce35fbd6faca1a0fe94bcd\transformed\jetified-core-viewtree-1.0.0\res
com.example.event_vendor_app-core-runtime-2.2.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\8d21b27548ea182ca928fd7916f7852a\transformed\core-runtime-2.2.0\res
com.example.event_vendor_app-jetified-core-common-2.0.3-27 C:\Users\<USER>\.gradle\caches\transforms-3\915af3f83c0df86715279d77bd5de169\transformed\jetified-core-common-2.0.3\res
com.example.event_vendor_app-jetified-play-services-basement-18.5.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\a12066cd0b0b35d6deebf50936ee2b0f\transformed\jetified-play-services-basement-18.5.0\res
com.example.event_vendor_app-fragment-1.7.1-29 C:\Users\<USER>\.gradle\caches\transforms-3\a2ae1d64140bd8512410079ef4200e9f\transformed\fragment-1.7.1\res
com.example.event_vendor_app-jetified-lifecycle-livedata-core-ktx-2.7.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\a4a687ba7495ff8183830b6343dc9aa8\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.event_vendor_app-jetified-activity-ktx-1.10.1-31 C:\Users\<USER>\.gradle\caches\transforms-3\a5dc3ccf1d23cc7a8776b8ceb9458529\transformed\jetified-activity-ktx-1.10.1\res
com.example.event_vendor_app-jetified-firebase-common-21.0.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\b061b7e1e3b00da29e57a2eccbb56fd3\transformed\jetified-firebase-common-21.0.0\res
com.example.event_vendor_app-lifecycle-livedata-core-2.7.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\b6adb4dd7652b38f144dae2f387ddf4c\transformed\lifecycle-livedata-core-2.7.0\res
com.example.event_vendor_app-lifecycle-viewmodel-2.7.0-34 C:\Users\<USER>\.gradle\caches\transforms-3\bfda056bf281db8bf4dae157ce0975a2\transformed\lifecycle-viewmodel-2.7.0\res
com.example.event_vendor_app-jetified-datastore-core-release-35 C:\Users\<USER>\.gradle\caches\transforms-3\c2bc2b6bf287c38d52ad26bb936f54bf\transformed\jetified-datastore-core-release\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-ktx-2.7.0-36 C:\Users\<USER>\.gradle\caches\transforms-3\c82f8f2b026377784812933cb7756280\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.event_vendor_app-coordinatorlayout-1.0.0-37 C:\Users\<USER>\.gradle\caches\transforms-3\c8d4aa401603afdfc3a705a5eb41ef8f\transformed\coordinatorlayout-1.0.0\res
com.example.event_vendor_app-jetified-savedstate-ktx-1.2.1-38 C:\Users\<USER>\.gradle\caches\transforms-3\c8d5a53c93a5ebedbaf51960d2667ea9\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.event_vendor_app-lifecycle-runtime-2.7.0-39 C:\Users\<USER>\.gradle\caches\transforms-3\d247c7f44bff2aa5d0125e0950f4fd76\transformed\lifecycle-runtime-2.7.0\res
com.example.event_vendor_app-preference-1.2.1-40 C:\Users\<USER>\.gradle\caches\transforms-3\d4185390180950757c3d08502d2fd14b\transformed\preference-1.2.1\res
com.example.event_vendor_app-browser-1.8.0-41 C:\Users\<USER>\.gradle\caches\transforms-3\d68f6fd17619c38bcf250daf71ab5ef6\transformed\browser-1.8.0\res
com.example.event_vendor_app-core-1.13.1-42 C:\Users\<USER>\.gradle\caches\transforms-3\d6db1a707cede35414abff3851a56c18\transformed\core-1.13.1\res
com.example.event_vendor_app-recyclerview-1.0.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\de4d037b89d934a181d7293521a67288\transformed\recyclerview-1.0.0\res
com.example.event_vendor_app-transition-1.4.1-44 C:\Users\<USER>\.gradle\caches\transforms-3\e0419b5c883b8d7c554773fafee3888d\transformed\transition-1.4.1\res
com.example.event_vendor_app-slidingpanelayout-1.2.0-45 C:\Users\<USER>\.gradle\caches\transforms-3\e2c6291cc9ec3baa49114e90dfe509b8\transformed\slidingpanelayout-1.2.0\res
com.example.event_vendor_app-localbroadcastmanager-1.1.0-46 C:\Users\<USER>\.gradle\caches\transforms-3\f01f5f23301779748b1b7a7873cf1bfe\transformed\localbroadcastmanager-1.1.0\res
com.example.event_vendor_app-debug-47 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\debug\res
com.example.event_vendor_app-main-48 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\res
com.example.event_vendor_app-pngs-49 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\pngs\debug
com.example.event_vendor_app-resValues-50 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\resValues\debug
com.example.event_vendor_app-packageDebugResources-51 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.event_vendor_app-packageDebugResources-52 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.event_vendor_app-debug-53 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.event_vendor_app-debug-54 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-55 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-56 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-57 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-58 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-59 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-60 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-61 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-62 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-63 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geocoding_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-64 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-65 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-66 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-67 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-68 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-69 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-70 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-71 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-72 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
