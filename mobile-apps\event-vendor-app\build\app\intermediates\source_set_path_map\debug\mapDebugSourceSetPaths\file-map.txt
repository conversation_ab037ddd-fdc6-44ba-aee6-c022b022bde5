com.example.event_vendor_app-lifecycle-runtime-2.7.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\02563fafe4b1ef0297b50d4b1162f2f9\transformed\lifecycle-runtime-2.7.0\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\05e4302111861d04679c8f32092ef022\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.event_vendor_app-jetified-core-ktx-1.13.1-2 C:\Users\<USER>\.gradle\caches\transforms-3\0b9840318b7711c527acc20a7a5f1257\transformed\jetified-core-ktx-1.13.1\res
com.example.event_vendor_app-jetified-play-services-basement-18.5.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\10147e66d4f57b3604295eae8c58c970\transformed\jetified-play-services-basement-18.5.0\res
com.example.event_vendor_app-lifecycle-viewmodel-2.7.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\115dfc8b26fcd9ac80d393dc53544964\transformed\lifecycle-viewmodel-2.7.0\res
com.example.event_vendor_app-jetified-credentials-play-services-auth-1.2.0-rc01-5 C:\Users\<USER>\.gradle\caches\transforms-3\1754b15f113ba7a4d38a5697a78ec2a0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.event_vendor_app-core-runtime-2.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\1bf05381c44630f5bc44e7558fb374a1\transformed\core-runtime-2.2.0\res
com.example.event_vendor_app-browser-1.8.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\1d22c42c87424ac020aa74c5e9d10e64\transformed\browser-1.8.0\res
com.example.event_vendor_app-appcompat-1.1.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\225f376fd76dacfb454bf2c5d451dcf4\transformed\appcompat-1.1.0\res
com.example.event_vendor_app-jetified-ads-adservices-1.1.0-beta11-9 C:\Users\<USER>\.gradle\caches\transforms-3\235e195bd68f758c97c567d97b363d42\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.event_vendor_app-jetified-core-1.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\2d1e7ebb153435f486a59c23154ef731\transformed\jetified-core-1.0.0\res
com.example.event_vendor_app-recyclerview-1.0.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\35acbbbb7b87cefcdd3843b36e14bb79\transformed\recyclerview-1.0.0\res
com.example.event_vendor_app-jetified-core-common-2.0.3-12 C:\Users\<USER>\.gradle\caches\transforms-3\3d3a8d6aaace0c606b993b2df3939fba\transformed\jetified-core-common-2.0.3\res
com.example.event_vendor_app-lifecycle-livedata-core-2.7.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\4704100fd1d32d3beb10deb1367297e1\transformed\lifecycle-livedata-core-2.7.0\res
com.example.event_vendor_app-jetified-lifecycle-process-2.7.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\485f5a25b7d697321a6c2a5ce1bda0b8\transformed\jetified-lifecycle-process-2.7.0\res
com.example.event_vendor_app-jetified-savedstate-1.2.1-15 C:\Users\<USER>\.gradle\caches\transforms-3\4d233891a671827bb2907e4d049510e3\transformed\jetified-savedstate-1.2.1\res
com.example.event_vendor_app-preference-1.2.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\50ed2967e2214bc7420465d6a51f3b13\transformed\preference-1.2.1\res
com.example.event_vendor_app-jetified-datastore-preferences-release-17 C:\Users\<USER>\.gradle\caches\transforms-3\5fb4df89452684ade6a3555eb64a711d\transformed\jetified-datastore-preferences-release\res
com.example.event_vendor_app-jetified-lifecycle-livedata-core-ktx-2.7.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\6209c0d55e17a1027f766b62ec49852f\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.event_vendor_app-core-1.13.1-19 C:\Users\<USER>\.gradle\caches\transforms-3\693a1137532c792072bb392c0e3bc56d\transformed\core-1.13.1\res
com.example.event_vendor_app-jetified-activity-1.10.1-20 C:\Users\<USER>\.gradle\caches\transforms-3\6f8b79774256371f7b5f62b7867e611a\transformed\jetified-activity-1.10.1\res
com.example.event_vendor_app-jetified-datastore-release-21 C:\Users\<USER>\.gradle\caches\transforms-3\72c88133a42689588e26230a8eae1df8\transformed\jetified-datastore-release\res
com.example.event_vendor_app-jetified-window-java-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\732cdb63b81c85d99009d973b0f0aeb9\transformed\jetified-window-java-1.2.0\res
com.example.event_vendor_app-jetified-appcompat-resources-1.1.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\7ba2b57dc7cda123647d56fb06ed8a19\transformed\jetified-appcompat-resources-1.1.0\res
com.example.event_vendor_app-jetified-ads-adservices-java-1.1.0-beta11-24 C:\Users\<USER>\.gradle\caches\transforms-3\7dace9ba036c3173246f81e6fdc4967c\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.event_vendor_app-localbroadcastmanager-1.1.0-25 C:\Users\<USER>\.gradle\caches\transforms-3\8d929d4e6f18cc9541409f4b1e0779f9\transformed\localbroadcastmanager-1.1.0\res
com.example.event_vendor_app-jetified-activity-ktx-1.10.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\94a972165b55f72aa09a71a9e2cd939d\transformed\jetified-activity-ktx-1.10.1\res
com.example.event_vendor_app-jetified-annotation-experimental-1.4.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\9553f273e71933ecad58e81b139244fe\transformed\jetified-annotation-experimental-1.4.0\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-ktx-2.7.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\9da6f624675eb766a158a2fca9aa1eae\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.event_vendor_app-jetified-play-services-auth-21.0.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\9dfa4fac73d8e6fc3b46ff9d6307a46e\transformed\jetified-play-services-auth-21.0.0\res
com.example.event_vendor_app-jetified-fragment-ktx-1.7.1-30 C:\Users\<USER>\.gradle\caches\transforms-3\9e986720a8b4340d75e349bd923b172f\transformed\jetified-fragment-ktx-1.7.1\res
com.example.event_vendor_app-jetified-core-viewtree-1.0.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\a480d40b9c8d019cc6388829f7e52252\transformed\jetified-core-viewtree-1.0.0\res
com.example.event_vendor_app-jetified-lifecycle-runtime-ktx-2.7.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\b4c71792c3bd12e30fd985471fb82541\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.event_vendor_app-jetified-play-services-base-18.5.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\b4ebdbca769560bba245800d38b3553d\transformed\jetified-play-services-base-18.5.0\res
com.example.event_vendor_app-jetified-firebase-messaging-24.1.2-34 C:\Users\<USER>\.gradle\caches\transforms-3\b69b3d8ee7a92c85e29a810b34c80141\transformed\jetified-firebase-messaging-24.1.2\res
com.example.event_vendor_app-slidingpanelayout-1.2.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\b6fe3d2c75e588161895f267379a5931\transformed\slidingpanelayout-1.2.0\res
com.example.event_vendor_app-jetified-window-1.2.0-36 C:\Users\<USER>\.gradle\caches\transforms-3\bf5ff3035f9ac93a0db283a7dd73f008\transformed\jetified-window-1.2.0\res
com.example.event_vendor_app-fragment-1.7.1-37 C:\Users\<USER>\.gradle\caches\transforms-3\cf4b5213e0ffa12fb40d1b2f5a12494d\transformed\fragment-1.7.1\res
com.example.event_vendor_app-jetified-profileinstaller-1.4.0-38 C:\Users\<USER>\.gradle\caches\transforms-3\d5bc28fe808b7973622d7480da66b8ee\transformed\jetified-profileinstaller-1.4.0\res
com.example.event_vendor_app-transition-1.4.1-39 C:\Users\<USER>\.gradle\caches\transforms-3\d6f4a420e0e8618ba24c85217ec2d85b\transformed\transition-1.4.1\res
com.example.event_vendor_app-jetified-tracing-1.2.0-40 C:\Users\<USER>\.gradle\caches\transforms-3\ea235edd654ebaf2e60ecbdeeef8b8d0\transformed\jetified-tracing-1.2.0\res
com.example.event_vendor_app-jetified-savedstate-ktx-1.2.1-41 C:\Users\<USER>\.gradle\caches\transforms-3\f1751a64bd8fbe52622b813451cb8ac5\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.event_vendor_app-jetified-startup-runtime-1.1.1-42 C:\Users\<USER>\.gradle\caches\transforms-3\f1e92a380f9a88d93d4ccd7271daba3e\transformed\jetified-startup-runtime-1.1.1\res
com.example.event_vendor_app-jetified-firebase-common-21.0.0-43 C:\Users\<USER>\.gradle\caches\transforms-3\f213df8967dd7d64f35e4af8c89b7950\transformed\jetified-firebase-common-21.0.0\res
com.example.event_vendor_app-jetified-credentials-1.2.0-rc01-44 C:\Users\<USER>\.gradle\caches\transforms-3\f2ab3d97f6976114afbee9b4807e5120\transformed\jetified-credentials-1.2.0-rc01\res
com.example.event_vendor_app-coordinatorlayout-1.0.0-45 C:\Users\<USER>\.gradle\caches\transforms-3\f999fea82211c7308daf6630bd828c71\transformed\coordinatorlayout-1.0.0\res
com.example.event_vendor_app-jetified-datastore-core-release-46 C:\Users\<USER>\.gradle\caches\transforms-3\fa0e4e88647abbc68e7da318fc6683a3\transformed\jetified-datastore-core-release\res
com.example.event_vendor_app-debug-47 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\debug\res
com.example.event_vendor_app-main-48 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\res
com.example.event_vendor_app-pngs-49 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\pngs\debug
com.example.event_vendor_app-resValues-50 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\resValues\debug
com.example.event_vendor_app-packageDebugResources-51 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.event_vendor_app-packageDebugResources-52 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.event_vendor_app-debug-53 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.event_vendor_app-debug-54 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-55 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-56 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-57 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-58 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-59 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-60 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-61 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-62 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-63 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geocoding_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-64 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-65 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-66 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-67 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-68 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-69 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-70 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-71 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\sqflite_android\intermediates\packaged_res\debug\packageDebugResources
com.example.event_vendor_app-debug-72 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\packaged_res\debug\packageDebugResources
