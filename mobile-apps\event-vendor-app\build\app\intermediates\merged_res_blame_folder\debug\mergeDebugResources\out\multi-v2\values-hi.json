{"logs": [{"outputFile": "com.example.event_vendor_app-mergeDebugResources-51:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b4ebdbca769560bba245800d38b3553d\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3763,3871,4031,4157,4269,4420,4550,4662,4920,5077,5186,5352,5482,5623,5776,5839,5906", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "3866,4026,4152,4264,4415,4545,4657,4769,5072,5181,5347,5477,5618,5771,5834,5901,5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\693a1137532c792072bb392c0e3bc56d\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3010,3108,3211,3316,3417,3530,3636,6882", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3103,3206,3311,3412,3525,3631,3758,6978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10147e66d4f57b3604295eae8c58c970\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4774", "endColumns": "145", "endOffsets": "4915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2ab3d97f6976114afbee9b4807e5120\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,111", "endOffsets": "161,273"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2787,2898", "endColumns": "110,111", "endOffsets": "2893,3005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d22c42c87424ac020aa74c5e9d10e64\\transformed\\browser-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6067,6264,6366,6478", "endColumns": "105,101,111,102", "endOffsets": "6168,6361,6473,6576"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\50ed2967e2214bc7420465d6a51f3b13\\transformed\\preference-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,489,658,738", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "173,264,342,484,653,733,811"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5994,6173,6581,6659,6983,7152,7232", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "6062,6259,6654,6796,7147,7227,7305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\225f376fd76dacfb454bf2c5d451dcf4\\transformed\\appcompat-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,6801", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,6877"}}]}]}