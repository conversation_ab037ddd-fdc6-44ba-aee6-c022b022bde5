import 'package:flutter/material.dart';
import '../config/app_theme.dart';

class AppLogo extends StatelessWidget {
  final double size;
  final bool showBackground;
  final EdgeInsets? padding;
  final List<BoxShadow>? boxShadow;

  const AppLogo({
    super.key,
    this.size = 36,
    this.showBackground = true,
    this.padding,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? EdgeInsets.all(size * 0.15);
    
    Widget logoWidget = Image.asset(
      'assets/logos/logo.png',
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to icon if asset fails to load
        return Icon(
          Icons.event_available,
          size: size * 0.6,
          color: showBackground ? AppTheme.primaryColor : Colors.white,
        );
      },
    );

    if (showBackground) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: boxShadow,
        ),
        child: Padding(
          padding: effectivePadding,
          child: logoWidget,
        ),
      );
    }

    return SizedBox(
      width: size,
      height: size,
      child: Padding(
        padding: effectivePadding,
        child: logoWidget,
      ),
    );
  }
}

// Predefined logo sizes for consistency
class AppLogoSizes {
  static const double header = 36;
  static const double login = 120;
  static const double splash = 140;
}
