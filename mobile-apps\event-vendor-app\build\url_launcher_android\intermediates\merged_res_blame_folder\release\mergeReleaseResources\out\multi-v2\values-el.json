{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4cf7f43b1fbc1e75f08fcb2008611c3d\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,1242", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,1338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37be4d6ba90a05589f451f5f9eb01978\\transformed\\browser-1.8.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "790,900,1007,1132", "endColumns": "109,106,124,109", "endOffsets": "895,1002,1127,1237"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4cf7f43b1fbc1e75f08fcb2008611c3d\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,1242", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,1338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37be4d6ba90a05589f451f5f9eb01978\\transformed\\browser-1.8.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "790,900,1007,1132", "endColumns": "109,106,124,109", "endOffsets": "895,1002,1127,1237"}}]}]}