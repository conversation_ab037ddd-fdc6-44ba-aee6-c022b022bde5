{"logs": [{"outputFile": "com.example.event_vendor_app-mergeDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0290aaab56035474dc93869f3b5bf01c\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2004", "startColumns": "4", "startOffsets": "130087", "endLines": "2007", "endColumns": "12", "endOffsets": "130305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de4d037b89d934a181d7293521a67288\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "232,233,234,242,243,244,319,3457", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13968,14027,14075,14742,14817,14893,19395,185477", "endLines": "232,233,234,242,243,244,319,3476", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14022,14070,14126,14812,14888,14960,19456,186267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8642c542a593364818c455b3d8eddcfb\\transformed\\jetified-firebase-messaging-24.1.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "430", "startColumns": "4", "startOffsets": "27397", "endColumns": "81", "endOffsets": "27474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\591da1a48a958d442215c4093bb458e6\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,361,362,363,364,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,435,440,441,442,443,444,445,453,454,458,462,466,471,477,484,488,492,497,501,505,509,513,517,521,527,531,537,541,547,551,556,560,563,567,573,577,583,587,593,596,600,604,608,612,616,617,618,619,622,625,628,631,635,636,637,638,639,642,644,646,648,653,654,658,664,668,669,671,682,683,687,693,697,698,699,703,730,734,735,739,767,937,963,1134,1160,1191,1199,1205,1219,1241,1246,1251,1261,1270,1279,1283,1290,1298,1305,1306,1315,1318,1321,1325,1329,1333,1336,1337,1342,1347,1357,1362,1369,1375,1376,1379,1383,1388,1390,1392,1395,1398,1400,1404,1407,1414,1417,1420,1424,1426,1430,1432,1434,1436,1440,1448,1456,1468,1474,1483,1486,1497,1500,1501,1506,1507,1536,1605,1675,1676,1686,1695,1847,1849,1853,1856,1859,1862,1865,1868,1871,1874,1878,1881,1884,1887,1891,1894,1898,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1924,1926,1927,1928,1929,1930,1931,1932,1933,1935,1936,1938,1939,1941,1943,1944,1946,1947,1948,1949,1950,1951,1953,1954,1955,1956,1957,1969,1971,1973,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1989,1990,1991,1992,1993,1994,1996,2000,2016,2017,2018,2019,2020,2021,2025,2026,2027,2028,2030,2032,2034,2036,2038,2039,2040,2041,2043,2045,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2061,2062,2063,2064,2066,2068,2069,2071,2072,2074,2076,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2091,2092,2093,2094,2096,2097,2098,2099,2100,2102,2104,2106,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2128,2203,2206,2209,2212,2226,2243,2285,2314,2341,2350,2412,2776,2807,2945,3069,3093,3099,3128,3149,3273,3301,3307,3451,3477,3544,3615,3715,3735,3790,3802,3828", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6446,6520,6599,6672,6746,6817,6889,6961,7034,7091,7149,7222,7296,7370,7445,7517,7590,7660,7731,7791,7852,7921,7990,8060,8134,8210,8274,8351,8427,8504,8569,8638,8715,8790,8859,8927,9004,9070,9131,9228,9293,9362,9461,9532,9591,9649,9706,9765,9829,9900,9972,10044,10116,10188,10255,10323,10391,10450,10513,10577,10667,10758,10818,10884,10951,11017,11087,11151,11204,11271,11332,11399,11512,11570,11633,11698,11763,11838,11911,11983,12032,12093,12154,12215,12277,12341,12405,12469,12534,12597,12657,12718,12784,12843,12903,12965,13036,13096,13795,13881,14131,14221,14308,14396,14478,14561,14651,16376,16428,16486,16531,16597,16661,16718,16775,18952,19009,19057,19106,19361,19731,19778,20036,21207,21553,21617,21679,21739,22060,22134,22204,22282,22336,22406,22491,22539,22585,22646,22709,22775,22839,22910,22973,23038,23102,23163,23224,23276,23349,23423,23492,23567,23641,23715,23856,27663,28024,28102,28192,28280,28376,28466,29048,29137,29384,29665,29917,30202,30595,31072,31294,31516,31792,32019,32249,32479,32709,32939,33166,33585,33811,34236,34466,34894,35113,35396,35604,35735,35962,36388,36613,37040,37261,37686,37806,38082,38383,38707,38998,39312,39449,39580,39685,39927,40094,40298,40506,40777,40889,41001,41106,41223,41437,41583,41723,41809,42157,42245,42491,42909,43158,43240,43338,43930,44030,44282,44706,44961,45055,45144,45381,47405,47647,47749,48002,50158,60690,62206,72837,74365,76122,76748,77168,78229,79494,79750,79986,80533,81027,81632,81830,82410,82974,83349,83467,84005,84162,84358,84631,84887,85057,85198,85262,85627,85994,86670,86934,87272,87625,87719,87905,88211,88473,88598,88725,88964,89175,89294,89487,89664,90119,90300,90422,90681,90794,90981,91083,91190,91319,91594,92102,92598,93475,93769,94339,94488,95220,95392,95476,95812,95904,97970,103216,108605,108667,109245,109829,117776,117889,118118,118278,118430,118601,118767,118936,119103,119266,119509,119679,119852,120023,120297,120496,120701,121031,121115,121211,121307,121405,121505,121607,121709,121811,121913,122015,122115,122211,122323,122452,122575,122706,122837,122935,123049,123143,123283,123417,123513,123625,123725,123841,123937,124049,124149,124289,124425,124589,124719,124877,125027,125168,125312,125447,125559,125709,125837,125965,126101,126233,126363,126493,126605,127503,127649,127793,127931,127997,128087,128163,128267,128357,128459,128567,128675,128775,128855,128947,129045,129155,129233,129339,129431,129535,129645,129767,129930,130720,130800,130900,130990,131100,131190,131431,131525,131631,131723,131823,131935,132049,132165,132281,132375,132489,132601,132703,132823,132945,133027,133131,133251,133377,133475,133569,133657,133769,133885,134007,134119,134294,134410,134496,134588,134700,134824,134891,135017,135085,135213,135357,135485,135554,135649,135764,135877,135976,136085,136196,136307,136408,136513,136613,136743,136834,136957,137051,137163,137249,137353,137449,137537,137655,137759,137863,137989,138077,138185,138285,138375,138485,138569,138671,138755,138809,138873,138979,139065,139175,139259,139663,142279,142397,142512,142592,142953,143539,144943,146287,147648,148036,150811,160900,161940,168753,173054,173805,174067,174914,175293,179571,180425,180654,185262,186272,188224,190624,194748,195492,197623,197963,199274", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,361,362,363,364,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,435,440,441,442,443,444,452,453,457,461,465,470,476,483,487,491,496,500,504,508,512,516,520,526,530,536,540,546,550,555,559,562,566,572,576,582,586,592,595,599,603,607,611,615,616,617,618,621,624,627,630,634,635,636,637,638,641,643,645,647,652,653,657,663,667,668,670,681,682,686,692,696,697,698,702,729,733,734,738,766,936,962,1133,1159,1190,1198,1204,1218,1240,1245,1250,1260,1269,1278,1282,1289,1297,1304,1305,1314,1317,1320,1324,1328,1332,1335,1336,1341,1346,1356,1361,1368,1374,1375,1378,1382,1387,1389,1391,1394,1397,1399,1403,1406,1413,1416,1419,1423,1425,1429,1431,1433,1435,1439,1447,1455,1467,1473,1482,1485,1496,1499,1500,1505,1506,1511,1604,1674,1675,1685,1694,1695,1848,1852,1855,1858,1861,1864,1867,1870,1873,1877,1880,1883,1886,1890,1893,1897,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1923,1925,1926,1927,1928,1929,1930,1931,1932,1934,1935,1937,1938,1940,1942,1943,1945,1946,1947,1948,1949,1950,1952,1953,1954,1955,1956,1957,1970,1972,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1988,1989,1990,1991,1992,1993,1995,1999,2003,2016,2017,2018,2019,2020,2024,2025,2026,2027,2029,2031,2033,2035,2037,2038,2039,2040,2042,2044,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2060,2061,2062,2063,2065,2067,2068,2070,2071,2073,2075,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2090,2091,2092,2093,2095,2096,2097,2098,2099,2101,2103,2105,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2202,2205,2208,2211,2225,2231,2252,2313,2340,2349,2411,2770,2779,2834,2962,3092,3098,3104,3148,3272,3292,3306,3310,3456,3511,3555,3680,3734,3789,3801,3827,3834", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6515,6594,6667,6741,6812,6884,6956,7029,7086,7144,7217,7291,7365,7440,7512,7585,7655,7726,7786,7847,7916,7985,8055,8129,8205,8269,8346,8422,8499,8564,8633,8710,8785,8854,8922,8999,9065,9126,9223,9288,9357,9456,9527,9586,9644,9701,9760,9824,9895,9967,10039,10111,10183,10250,10318,10386,10445,10508,10572,10662,10753,10813,10879,10946,11012,11082,11146,11199,11266,11327,11394,11507,11565,11628,11693,11758,11833,11906,11978,12027,12088,12149,12210,12272,12336,12400,12464,12529,12592,12652,12713,12779,12838,12898,12960,13031,13091,13159,13876,13963,14216,14303,14391,14473,14556,14646,14737,16423,16481,16526,16592,16656,16713,16770,16824,19004,19052,19101,19152,19390,19773,19822,20077,21234,21612,21674,21734,21791,22129,22199,22277,22331,22401,22486,22534,22580,22641,22704,22770,22834,22905,22968,23033,23097,23158,23219,23271,23344,23418,23487,23562,23636,23710,23851,23921,27711,28097,28187,28275,28371,28461,29043,29132,29379,29660,29912,30197,30590,31067,31289,31511,31787,32014,32244,32474,32704,32934,33161,33580,33806,34231,34461,34889,35108,35391,35599,35730,35957,36383,36608,37035,37256,37681,37801,38077,38378,38702,38993,39307,39444,39575,39680,39922,40089,40293,40501,40772,40884,40996,41101,41218,41432,41578,41718,41804,42152,42240,42486,42904,43153,43235,43333,43925,44025,44277,44701,44956,45050,45139,45376,47400,47642,47744,47997,50153,60685,62201,72832,74360,76117,76743,77163,78224,79489,79745,79981,80528,81022,81627,81825,82405,82969,83344,83462,84000,84157,84353,84626,84882,85052,85193,85257,85622,85989,86665,86929,87267,87620,87714,87900,88206,88468,88593,88720,88959,89170,89289,89482,89659,90114,90295,90417,90676,90789,90976,91078,91185,91314,91589,92097,92593,93470,93764,94334,94483,95215,95387,95471,95807,95899,96177,103211,108600,108662,109240,109824,109915,117884,118113,118273,118425,118596,118762,118931,119098,119261,119504,119674,119847,120018,120292,120491,120696,121026,121110,121206,121302,121400,121500,121602,121704,121806,121908,122010,122110,122206,122318,122447,122570,122701,122832,122930,123044,123138,123278,123412,123508,123620,123720,123836,123932,124044,124144,124284,124420,124584,124714,124872,125022,125163,125307,125442,125554,125704,125832,125960,126096,126228,126358,126488,126600,126740,127644,127788,127926,127992,128082,128158,128262,128352,128454,128562,128670,128770,128850,128942,129040,129150,129228,129334,129426,129530,129640,129762,129925,130082,130795,130895,130985,131095,131185,131426,131520,131626,131718,131818,131930,132044,132160,132276,132370,132484,132596,132698,132818,132940,133022,133126,133246,133372,133470,133564,133652,133764,133880,134002,134114,134289,134405,134491,134583,134695,134819,134886,135012,135080,135208,135352,135480,135549,135644,135759,135872,135971,136080,136191,136302,136403,136508,136608,136738,136829,136952,137046,137158,137244,137348,137444,137532,137650,137754,137858,137984,138072,138180,138280,138370,138480,138564,138666,138750,138804,138868,138974,139060,139170,139254,139374,142274,142392,142507,142587,142948,143181,144051,146282,147643,148031,150806,160710,161030,163292,169320,173800,174062,174262,175288,179566,180172,180649,180800,185472,187350,188531,193645,195487,197618,197958,199269,199472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a12066cd0b0b35d6deebf50936ee2b0f\\transformed\\jetified-play-services-basement-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "365,414", "startColumns": "4,4", "startOffsets": "21796,25726", "endColumns": "67,166", "endOffsets": "21859,25888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\335c87e37d5f76be4d8c366f023c01e0\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "328,357", "startColumns": "4,4", "startOffsets": "19827,21325", "endColumns": "41,59", "endOffsets": "19864,21380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d6db1a707cede35414abff3851a56c18\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,368,399,400,401,402,403,404,405,436,1958,1959,1963,1964,1968,2123,2124,2780,2797,2967,3000,3030,3063", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13307,13377,13445,13517,13587,13648,13722,14965,15026,15087,15149,15213,15275,15336,15404,15504,15564,15630,15703,15772,15829,15881,16829,16901,16977,17042,17101,17160,17220,17280,17340,17400,17460,17520,17580,17640,17700,17760,17819,17879,17939,17999,18059,18119,18179,18239,18299,18359,18419,18478,18538,18598,18657,18716,18775,18834,18893,19461,19496,20082,20137,20200,20255,20313,20371,20432,20495,20552,20603,20653,20714,20771,20837,20871,20906,21990,24175,24242,24314,24383,24452,24526,24598,27716,126745,126862,127063,127173,127374,139379,139451,161035,161639,169474,171205,172205,172887", "endLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,368,399,400,401,402,403,404,405,436,1958,1962,1963,1967,1968,2123,2124,2785,2806,2999,3020,3062,3068", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13372,13440,13512,13582,13643,13717,13790,15021,15082,15144,15208,15270,15331,15399,15499,15559,15625,15698,15767,15824,15876,15938,16896,16972,17037,17096,17155,17215,17275,17335,17395,17455,17515,17575,17635,17695,17755,17814,17874,17934,17994,18054,18114,18174,18234,18294,18354,18414,18473,18533,18593,18652,18711,18770,18829,18888,18947,19491,19526,20132,20195,20250,20308,20366,20427,20490,20547,20598,20648,20709,20766,20832,20866,20901,20936,22055,24237,24309,24378,24447,24521,24593,24681,27782,126857,127058,127168,127369,127498,139446,139513,161233,161935,171200,171881,172882,173049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a2ae1d64140bd8512410079ef4200e9f\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "315,331,360,3021,3026", "startColumns": "4,4,4,4,4", "startOffsets": "19217,19971,21489,171886,172056", "endLines": "315,331,360,3025,3029", "endColumns": "56,64,63,24,24", "endOffsets": "19269,20031,21548,172051,172200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8d4aa401603afdfc3a705a5eb41ef8f\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2125,2835,2841", "startColumns": "4,4,4,4", "startOffsets": "164,139518,163297,163508", "endLines": "3,2127,2840,2924", "endColumns": "60,12,24,24", "endOffsets": "220,139658,163503,168019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\915af3f83c0df86715279d77bd5de169\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2008", "startColumns": "4", "startOffsets": "130310", "endLines": "2015", "endColumns": "8", "endOffsets": "130715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4fe6c5523c9733b800eb6ce889d7702a\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21385", "endColumns": "53", "endOffsets": "21434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4185390180950757c3d08502d2fd14b\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,122,260,261,262,263,264,265,266,323,324,325,366,367,424,426,431,432,437,438,439,1512,1696,1699,1705,1711,1714,1720,1724,1727,1734,1740,1743,1749,1754,1759,1766,1768,1774,1780,1788,1793,1800,1805,1811,1815,1822,1826,1832,1838,1841,1845,1846,2771,2786,2925,2963,3105,3293,3311,3375,3385,3395,3402,3408,3512,3681,3698", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6377,15943,16007,16062,16130,16197,16262,16319,19574,19622,19670,21864,21927,27019,27125,27479,27523,27787,27926,27976,96182,109920,110025,110270,110608,110754,111094,111306,111469,111876,112214,112337,112676,112915,113172,113543,113603,113941,114227,114676,114968,115356,115661,116005,116250,116580,116787,117055,117328,117472,117673,117720,160715,161238,168024,169325,174267,180177,180805,182730,183012,183317,183579,183839,187355,193650,194180", "endLines": "63,122,260,261,262,263,264,265,266,323,324,325,366,367,424,426,431,434,437,438,439,1528,1698,1704,1710,1713,1719,1723,1726,1733,1739,1742,1748,1753,1758,1765,1767,1773,1779,1787,1792,1799,1804,1810,1814,1821,1825,1831,1837,1840,1844,1845,1846,2775,2796,2944,2966,3114,3300,3374,3384,3394,3401,3407,3450,3524,3697,3714", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6441,16002,16057,16125,16192,16257,16314,16371,19617,19665,19726,21922,21985,27052,27177,27518,27658,27921,27971,28019,97615,110020,110265,110603,110749,111089,111301,111464,111871,112209,112332,112671,112910,113167,113538,113598,113936,114222,114671,114963,115351,115656,116000,116245,116575,116782,117050,117323,117467,117668,117715,117771,160895,161634,168748,169469,174594,180420,182725,183007,183312,183574,183834,185257,187802,194175,194743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e0419b5c883b8d7c554773fafee3888d\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "316,317,322,329,330,349,350,351,352,353", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19274,19314,19531,19869,19924,20941,20995,21047,21096,21157", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19309,19356,19569,19919,19966,20990,21042,21091,21152,21202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bfda056bf281db8bf4dae157ce0975a2\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "21439", "endColumns": "49", "endOffsets": "21484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\048d63f2815195511c50e00fb12469d6\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2253,2269,2275,3595,3611", "startColumns": "4,4,4,4,4", "startOffsets": "144056,144481,144659,190086,190497", "endLines": "2268,2274,2284,3610,3614", "endColumns": "24,24,24,24,24", "endOffsets": "144476,144654,144938,190492,190619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\34bc28d8be94b86ee5f74414228109cb\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "398", "startColumns": "4", "startOffsets": "24092", "endColumns": "82", "endOffsets": "24170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b5eb4bf95253b88a2aad39e0426e67c\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,406,407,408,409,410,411,412,413,415,416,417,418,419,420,421,422,423,3115,3525", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,24686,24791,24972,25097,25204,25384,25507,25623,25893,26081,26186,26367,26492,26667,26815,26878,26940,174599,187807", "endLines": "90,91,92,93,94,95,96,97,406,407,408,409,410,411,412,413,415,416,417,418,419,420,421,422,423,3127,3543", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,24786,24967,25092,25199,25379,25502,25618,25721,26076,26181,26362,26487,26662,26810,26873,26935,27014,174909,188219"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d68f6fd17619c38bcf250daf71ab5ef6\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,221,222,425,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13164,13235,27057,27182,27249,27328", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13230,13302,27120,27244,27323,27392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d247c7f44bff2aa5d0125e0950f4fd76\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "356", "startColumns": "4", "startOffsets": "21282", "endColumns": "42", "endOffsets": "21320"}}, {"source": "E:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1529,1533", "startColumns": "4,4", "startOffsets": "97620,97801", "endLines": "1532,1535", "endColumns": "12,12", "endOffsets": "97796,97965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\77b0af5e1299779a3a9698a95990dce8\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "396,397", "startColumns": "4,4", "startOffsets": "23926,24008", "endColumns": "81,83", "endOffsets": "24003,24087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3aac23582bac27d9b74d172d5afaf272\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,314,2232,2238,3556,3564,3579", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19157,143186,143381,188536,188818,189432", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,314,2237,2242,3563,3578,3594", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19212,143376,143534,188813,189427,190081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ca40b4206ce35fbd6faca1a0fe94bcd\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "21239", "endColumns": "42", "endOffsets": "21277"}}]}]}