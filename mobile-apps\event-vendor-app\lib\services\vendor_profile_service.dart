import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/vendor_model.dart';
import '../config/firebase_config.dart';

class VendorProfileService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Update vendor profile information
  Future<bool> updateVendorProfile({
    required String vendorId,
    required VendorProfile profile,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating vendor profile: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'profile': profile.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Vendor profile updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating vendor profile: $e');
      }
      return false;
    }
  }

  // Update business details
  Future<bool> updateBusinessDetails({
    required String vendorId,
    required BusinessDetails businessDetails,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating business details: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'businessDetails': businessDetails.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Business details updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating business details: $e');
      }
      return false;
    }
  }

  // Update services offered
  Future<bool> updateServices({
    required String vendorId,
    required List<String> services,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating services: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'services': services,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Services updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating services: $e');
      }
      return false;
    }
  }

  // Update portfolio/gallery
  Future<bool> updatePortfolio({
    required String vendorId,
    required List<String> portfolioImages,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating portfolio: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'portfolioImages': portfolioImages,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Portfolio updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating portfolio: $e');
      }
      return false;
    }
  }

  // Update vendor settings
  Future<bool> updateVendorSettings({
    required String vendorId,
    required VendorSettings settings,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating vendor settings: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'settings': settings.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Vendor settings updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating vendor settings: $e');
      }
      return false;
    }
  }

  // Update verification status
  Future<bool> updateVerificationStatus({
    required String vendorId,
    required VerificationStatus verificationStatus,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating verification status: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'verificationStatus': verificationStatus.toMap(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Verification status updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating verification status: $e');
      }
      return false;
    }
  }

  // Update complete vendor model
  Future<bool> updateCompleteVendor({
    required String vendorId,
    required VendorModel vendor,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating complete vendor: $vendorId');
      }

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .set(vendor.copyWith(updatedAt: DateTime.now()).toFirestore());

      if (kDebugMode) {
        print('✅ Complete vendor updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating complete vendor: $e');
      }
      return false;
    }
  }

  // Get vendor by ID
  Future<VendorModel?> getVendorById(String vendorId) async {
    try {
      final doc = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .get();

      if (doc.exists) {
        return VendorModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting vendor: $e');
      }
      return null;
    }
  }

  // Update specific fields
  Future<bool> updateVendorFields({
    required String vendorId,
    required Map<String, dynamic> fields,
  }) async {
    try {
      if (kDebugMode) {
        print('🔵 Updating vendor fields: $vendorId');
      }

      final updateData = Map<String, dynamic>.from(fields);
      updateData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update(updateData);

      if (kDebugMode) {
        print('✅ Vendor fields updated successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating vendor fields: $e');
      }
      return false;
    }
  }

  // Update profile completion status
  Future<bool> updateProfileCompletion({
    required String vendorId,
    required double completionPercentage,
    required List<String> completedSections,
    required List<String> pendingSections,
  }) async {
    try {
      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(vendorId)
          .update({
        'profileCompletion': {
          'percentage': completionPercentage,
          'completedSections': completedSections,
          'pendingSections': pendingSections,
          'lastUpdated': FieldValue.serverTimestamp(),
        },
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Profile completion updated: ${completionPercentage.toInt()}%');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating profile completion: $e');
      }
      return false;
    }
  }

  // Calculate profile completion percentage
  double calculateProfileCompletion(VendorModel vendor) {
    int totalFields = 0;
    int completedFields = 0;

    // Profile fields
    totalFields += 6; // name, email, phone, businessName, description, address
    if (vendor.profile.name.isNotEmpty) completedFields++;
    if (vendor.email.isNotEmpty) completedFields++;
    if (vendor.phoneNumber?.isNotEmpty == true) completedFields++;
    if (vendor.profile.businessName.isNotEmpty) completedFields++;
    if (vendor.profile.description.isNotEmpty) completedFields++;
    if (vendor.profile.address.isNotEmpty) completedFields++;

    // Business details
    totalFields += 4; // gstNumber, panNumber, businessType, establishedYear
    if (vendor.businessDetails.gstNumber?.isNotEmpty == true) completedFields++;
    if (vendor.businessDetails.panNumber?.isNotEmpty == true) completedFields++;
    if (vendor.businessDetails.businessType.isNotEmpty) completedFields++;
    if (vendor.businessDetails.establishedYear > 0) completedFields++;

    // Services
    totalFields += 1;
    if (vendor.services.isNotEmpty) completedFields++;

    // Portfolio
    totalFields += 1;
    if (vendor.portfolioImages.isNotEmpty) completedFields++;

    // Verification
    totalFields += 2; // email verified, phone verified
    if (vendor.verificationStatus.emailVerified) completedFields++;
    if (vendor.verificationStatus.phoneVerified) completedFields++;

    return (completedFields / totalFields) * 100;
  }

  // Get all vendors (for admin purposes)
  Future<List<VendorModel>> getAllVendors() async {
    try {
      final querySnapshot = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => VendorModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all vendors: $e');
      }
      return [];
    }
  }

  // Search vendors by criteria
  Future<List<VendorModel>> searchVendors({
    String? businessName,
    String? city,
    String? serviceType,
    double? minRating,
  }) async {
    try {
      Query query =
          _firestore.collection(FirebaseConfig.eventVendorsCollection);

      if (businessName != null && businessName.isNotEmpty) {
        query = query
            .where('profile.businessName', isGreaterThanOrEqualTo: businessName)
            .where('profile.businessName',
                isLessThanOrEqualTo: businessName + '\uf8ff');
      }

      if (city != null && city.isNotEmpty) {
        query = query.where('profile.city', isEqualTo: city);
      }

      if (minRating != null) {
        query = query.where('rating', isGreaterThanOrEqualTo: minRating);
      }

      final querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => VendorModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error searching vendors: $e');
      }
      return [];
    }
  }
}
