import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/vendor_model.dart';
import '../config/firebase_config.dart';
import 'sms_service.dart';

class VendorAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    // Web client ID from google-services.json - required for web platform
    clientId: kIsWeb
        ? '************-sj2fdnij7tpi1pgkcp6qi8dtdvi2a9t4.apps.googleusercontent.com'
        : null, // For mobile, client ID comes from google-services.json automatically
    scopes: [
      'email',
      'profile',
    ],
  );
  final SMSService _smsService = SMSService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current vendor from Firestore
  Future<VendorModel?> getCurrentVendor() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final doc = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(user.uid)
          .get();

      if (doc.exists) {
        return VendorModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current vendor: $e');
      }
      return null;
    }
  }

  // Auth state stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Get current vendor model
  Future<VendorModel?> getCurrentVendorModel() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final doc = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(user.uid)
          .get();

      if (doc.exists) {
        return VendorModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting vendor model: $e');
      }
      return null;
    }
  }

  // Sign in with email and password
  Future<VendorAuthResult> signInWithEmailPassword(
      String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        final vendorModel = await getCurrentVendorModel();
        return VendorAuthResult.success(vendorModel);
      }

      return VendorAuthResult.failure('Sign in failed');
    } on FirebaseAuthException catch (e) {
      return VendorAuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      return VendorAuthResult.failure('An unexpected error occurred');
    }
  }

  // Sign up with email and password
  Future<VendorAuthResult> signUpWithEmailPassword(
    String email,
    String password,
    String businessName,
    String firstName,
    String lastName,
  ) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Create vendor profile
        final vendorModel = VendorModel(
          uid: credential.user!.uid,
          email: email,
          displayName: '$firstName $lastName',
          isVerified: false,
          aadhaarVerified: false,
          isApproved: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          profile: VendorProfile(
            businessName: businessName,
            firstName: firstName,
            lastName: lastName,
            description: '',
            serviceAreas: [],
            specializations: [],
            eventTypes: [],
            address: '',
            city: '',
            state: '',
            pincode: '',
            name: '$firstName $lastName',
          ),
          businessDetails: BusinessDetails(
            licenseNumber: '',
            establishedYear: DateTime.now().year,
            employeeCount: 0,
            businessAddress: Address(
              street: '',
              city: '',
              state: '',
              country: 'India',
              pincode: '',
            ),
            operatingHours: _getDefaultOperatingHours(),
            businessType: '',
            annualRevenue: 0.0,
          ),
          documents: VendorDocuments(
            aadhaarCard: '',
            businessLicense: '',
            bankPassbook: '',
            photos: [],
          ),
          rating: 0.0,
          totalBookings: 0,
          stats: VendorStats(
            totalEvents: 0,
            completedEvents: 0,
            cancelledEvents: 0,
            averageRating: 0.0,
            totalReviews: 0,
            totalEarnings: 0.0,
            pendingCommissions: 0.0,
            activeEmployees: 0,
            completionRate: 0.0,
          ),
          services: [],
          portfolioImages: [],
          verificationStatus: VerificationStatus(
            emailVerified: false,
            phoneVerified: false,
            aadhaarVerified: false,
            businessVerified: false,
            bankVerified: false,
          ),
        );

        // Save to Firestore
        await _firestore
            .collection(FirebaseConfig.eventVendorsCollection)
            .doc(credential.user!.uid)
            .set(vendorModel.toFirestore());

        // Send email verification
        await credential.user!.sendEmailVerification();

        return VendorAuthResult.success(vendorModel);
      }

      return VendorAuthResult.failure('Sign up failed');
    } on FirebaseAuthException catch (e) {
      return VendorAuthResult.failure(_getAuthErrorMessage(e.code));
    } catch (e) {
      return VendorAuthResult.failure('An unexpected error occurred');
    }
  }

  // Get Google Cloud Console URLs for API management
  Map<String, String> getGoogleCloudConsoleUrls() {
    const projectId = '************'; // From google-services.json
    return {
      'peopleApi':
          'https://console.developers.google.com/apis/api/people.googleapis.com/overview?project=$projectId',
      'apiLibrary':
          'https://console.developers.google.com/apis/library?project=$projectId',
      'credentials':
          'https://console.developers.google.com/apis/credentials?project=$projectId',
      'oauth':
          'https://console.developers.google.com/apis/credentials/consent?project=$projectId',
    };
  }

  // Test Google Sign-In configuration
  Future<Map<String, dynamic>> testGoogleSignInConfig() async {
    try {
      if (kDebugMode) {
        print('🔵 Testing Google Sign-In configuration...');
        print('   Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
        print('   Client ID: ${_googleSignIn.clientId ?? 'Auto-configured'}');
      }

      // Try to initialize without signing in
      await _googleSignIn.signOut(); // Ensure clean state

      return {
        'success': true,
        'message': 'Google Sign-In configuration is valid',
        'platform': kIsWeb ? 'web' : 'mobile',
        'clientId': _googleSignIn.clientId ?? 'auto-configured',
        'consoleUrls': getGoogleCloudConsoleUrls(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Google Sign-In configuration test failed: $e');
      }

      final urls = getGoogleCloudConsoleUrls();
      String helpMessage = 'Google Sign-In configuration error: $e';

      if (e.toString().contains('People API')) {
        helpMessage =
            'People API is disabled. Enable it at: ${urls['peopleApi']}';
      }

      return {
        'success': false,
        'message': helpMessage,
        'error': e.toString(),
        'consoleUrls': urls,
      };
    }
  }

  // Sign in with Google
  Future<VendorAuthResult> signInWithGoogle() async {
    try {
      if (kDebugMode) {
        print('🔵 Starting Google Sign-In process...');
        print('   Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
        print('   Client ID: ${_googleSignIn.clientId ?? 'Auto-configured'}');
      }

      // Sign out first to ensure clean state
      await _googleSignIn.signOut();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        if (kDebugMode) {
          print('❌ Google sign in was cancelled by user');
        }
        return VendorAuthResult.failure('Google sign in was cancelled');
      }

      if (kDebugMode) {
        print('✅ Google user signed in: ${googleUser.email}');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (kDebugMode) {
        print('✅ Got Google authentication tokens');
        print(
            '   Access Token: ${googleAuth.accessToken?.substring(0, 20)}...');
        print('   ID Token: ${googleAuth.idToken?.substring(0, 20)}...');
      }

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      if (kDebugMode) {
        print('🔵 Signing in with Firebase credential...');
      }

      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        if (kDebugMode) {
          print('✅ Firebase authentication successful');
          print('   User ID: ${userCredential.user!.uid}');
          print('   Email: ${userCredential.user!.email}');
          print('   Display Name: ${userCredential.user!.displayName}');
        }

        // Check if vendor exists in Firestore
        if (kDebugMode) {
          print('🔵 Checking if vendor exists in Firestore...');
        }

        final doc = await _firestore
            .collection(FirebaseConfig.eventVendorsCollection)
            .doc(userCredential.user!.uid)
            .get();

        VendorModel vendorModel;

        if (!doc.exists) {
          if (kDebugMode) {
            print('🔵 Creating new vendor profile...');
          }

          // Create new vendor profile
          final names =
              userCredential.user!.displayName?.split(' ') ?? ['', ''];
          vendorModel = VendorModel(
            uid: userCredential.user!.uid,
            email: userCredential.user!.email ?? '',
            displayName: userCredential.user!.displayName,
            photoURL: userCredential.user!.photoURL,
            isVerified: userCredential.user!.emailVerified,
            aadhaarVerified: false,
            isApproved: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            profile: VendorProfile(
              businessName: '',
              firstName: names.isNotEmpty ? names[0] : '',
              lastName: names.length > 1 ? names.sublist(1).join(' ') : '',
              description: '',
              serviceAreas: [],
              specializations: [],
              eventTypes: [],
              address: '',
              city: '',
              state: '',
              pincode: '',
              name: names.isNotEmpty ? names.join(' ') : '',
            ),
            businessDetails: BusinessDetails(
              licenseNumber: '',
              establishedYear: DateTime.now().year,
              employeeCount: 0,
              businessAddress: Address(
                street: '',
                city: '',
                state: '',
                country: 'India',
                pincode: '',
              ),
              operatingHours: _getDefaultOperatingHours(),
              businessType: '',
              annualRevenue: 0.0,
            ),
            documents: VendorDocuments(
              aadhaarCard: '',
              businessLicense: '',
              bankPassbook: '',
              photos: [],
            ),
            rating: 0.0,
            totalBookings: 0,
            stats: VendorStats(
              totalEvents: 0,
              completedEvents: 0,
              cancelledEvents: 0,
              averageRating: 0.0,
              totalReviews: 0,
              totalEarnings: 0.0,
              pendingCommissions: 0.0,
              activeEmployees: 0,
              completionRate: 0.0,
            ),
            services: [],
            portfolioImages: [],
            verificationStatus: VerificationStatus(
              emailVerified: userCredential.user!.emailVerified,
              phoneVerified: false,
              aadhaarVerified: false,
              businessVerified: false,
              bankVerified: false,
            ),
          );

          // Save to Firestore
          await _firestore
              .collection(FirebaseConfig.eventVendorsCollection)
              .doc(userCredential.user!.uid)
              .set(vendorModel.toFirestore());

          if (kDebugMode) {
            print('✅ New vendor profile created and saved to Firestore');
          }
        } else {
          if (kDebugMode) {
            print('✅ Existing vendor profile loaded from Firestore');
          }
          vendorModel = VendorModel.fromFirestore(doc);
        }

        if (kDebugMode) {
          print('🎉 Google Sign-In completed successfully!');
          print('   Vendor ID: ${vendorModel.uid}');
          print('   Business Name: ${vendorModel.profile.businessName}');
        }

        return VendorAuthResult.success(vendorModel);
      }

      if (kDebugMode) {
        print('❌ Firebase authentication failed - no user returned');
      }
      return VendorAuthResult.failure(
          'Google sign in failed - no user returned');
    } catch (e) {
      if (kDebugMode) {
        print('❌ Google sign in error: $e');
        print('   Error type: ${e.runtimeType}');
        if (e is FirebaseAuthException) {
          print('   Firebase error code: ${e.code}');
          print('   Firebase error message: ${e.message}');
        }
      }

      String errorMessage = 'Google sign in failed';
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'account-exists-with-different-credential':
            errorMessage =
                'An account already exists with a different sign-in method';
            break;
          case 'invalid-credential':
            errorMessage = 'Invalid Google credentials';
            break;
          case 'operation-not-allowed':
            errorMessage = 'Google sign-in is not enabled';
            break;
          case 'user-disabled':
            errorMessage = 'This account has been disabled';
            break;
          case 'user-not-found':
            errorMessage = 'No account found with this email';
            break;
          case 'wrong-password':
            errorMessage = 'Incorrect password';
            break;
          case 'network-request-failed':
            errorMessage = 'Network error. Please check your connection';
            break;
          default:
            errorMessage = e.message ?? 'Google sign in failed';
        }
      } else if (e.toString().contains('People API has not been used') ||
          e.toString().contains('SERVICE_DISABLED')) {
        errorMessage =
            'Google People API is disabled. Please enable it in Google Cloud Console and try again.';
      } else if (e.toString().contains('PERMISSION_DENIED')) {
        errorMessage =
            'Permission denied. Please check Google Cloud Console API settings.';
      } else if (e.toString().contains('network')) {
        errorMessage = 'Network error. Please check your internet connection';
      }

      return VendorAuthResult.failure(errorMessage);
    }
  }

  // Send OTP for phone verification
  Future<VendorAuthResult> sendPhoneOTP(String phoneNumber) async {
    try {
      await _smsService.sendOTP(phoneNumber, 'login');
      return VendorAuthResult.success(null, message: 'OTP sent successfully');
    } catch (e) {
      return VendorAuthResult.failure('Failed to send OTP');
    }
  }

  // Verify phone OTP and sign in
  Future<VendorAuthResult> verifyPhoneOTP(
      String phoneNumber, String otp) async {
    try {
      final isValid = await _smsService.verifyOTP(phoneNumber, otp, 'login');

      if (!isValid) {
        return VendorAuthResult.failure('Invalid OTP');
      }

      // Check if vendor exists with this phone number
      final querySnapshot = await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .where('phoneNumber', isEqualTo: phoneNumber)
          .limit(1)
          .get();

      VendorModel vendorModel;

      if (querySnapshot.docs.isEmpty) {
        // Create new vendor with phone number
        final user = await _auth.signInAnonymously();

        vendorModel = VendorModel(
          uid: user.user!.uid,
          email: '',
          phoneNumber: phoneNumber,
          isVerified: true, // Phone is verified
          aadhaarVerified: false,
          isApproved: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          profile: VendorProfile(
            businessName: '',
            firstName: '',
            lastName: '',
            description: '',
            serviceAreas: [],
            specializations: [],
            eventTypes: [],
            address: '',
            city: '',
            state: '',
            pincode: '',
            name: '',
          ),
          businessDetails: BusinessDetails(
            licenseNumber: '',
            establishedYear: DateTime.now().year,
            employeeCount: 0,
            businessAddress: Address(
              street: '',
              city: '',
              state: '',
              country: 'India',
              pincode: '',
            ),
            operatingHours: _getDefaultOperatingHours(),
            businessType: '',
            annualRevenue: 0.0,
          ),
          documents: VendorDocuments(
            aadhaarCard: '',
            businessLicense: '',
            bankPassbook: '',
            photos: [],
          ),
          rating: 0.0,
          totalBookings: 0,
          stats: VendorStats(
            totalEvents: 0,
            completedEvents: 0,
            cancelledEvents: 0,
            averageRating: 0.0,
            totalReviews: 0,
            totalEarnings: 0.0,
            pendingCommissions: 0.0,
            activeEmployees: 0,
            completionRate: 0.0,
          ),
          services: [],
          portfolioImages: [],
          verificationStatus: VerificationStatus(
            emailVerified: false,
            phoneVerified: true, // Phone is verified
            aadhaarVerified: false,
            businessVerified: false,
            bankVerified: false,
          ),
        );

        // Save to Firestore
        await _firestore
            .collection(FirebaseConfig.eventVendorsCollection)
            .doc(user.user!.uid)
            .set(vendorModel.toFirestore());
      } else {
        vendorModel = VendorModel.fromFirestore(querySnapshot.docs.first);

        // Sign in the existing vendor
        await _auth.signInAnonymously();
      }

      return VendorAuthResult.success(vendorModel);
    } catch (e) {
      return VendorAuthResult.failure('Phone verification failed');
    }
  }

  // Update vendor profile
  Future<VendorAuthResult> updateVendorProfile(VendorModel vendorModel) async {
    try {
      final user = currentUser;
      if (user == null) {
        return VendorAuthResult.failure('No user signed in');
      }

      // Update Firebase Auth profile
      await user.updateDisplayName(vendorModel.displayName);
      if (vendorModel.photoURL != null) {
        await user.updatePhotoURL(vendorModel.photoURL);
      }

      // Update Firestore document
      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(user.uid)
          .update(
              vendorModel.copyWith(updatedAt: DateTime.now()).toFirestore());

      return VendorAuthResult.success(vendorModel);
    } catch (e) {
      return VendorAuthResult.failure('Failed to update profile');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('Sign out error: $e');
      }
    }
  }

  // Helper method to get default operating hours
  Map<String, OperatingHours> _getDefaultOperatingHours() {
    return {
      'monday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'tuesday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'wednesday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'thursday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'friday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'saturday': OperatingHours(open: '09:00', close: '18:00', isOpen: true),
      'sunday': OperatingHours(open: '09:00', close: '18:00', isOpen: false),
    };
  }

  // Get auth error message
  String _getAuthErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No vendor found with this email';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'Email is already registered';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later';
      case 'operation-not-allowed':
        return 'This operation is not allowed';
      default:
        return 'Authentication failed';
    }
  }

  // Update Aadhar verification status
  Future<bool> updateAadharVerification(
      String aadharNumber, Map<String, dynamic> verificationData) async {
    try {
      final user = currentUser;
      if (user == null) return false;

      await _firestore
          .collection(FirebaseConfig.eventVendorsCollection)
          .doc(user.uid)
          .update({
        'aadhaarNumber': aadharNumber,
        'aadhaarVerified': true,
        'aadhaarVerificationData': {
          'name': verificationData['name'],
          'dateOfBirth': verificationData['dateOfBirth'],
          'gender': verificationData['gender'],
          'address': verificationData['address'],
          'verifiedAt': verificationData['verifiedAt'],
        },
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('✅ Aadhar verification updated for user: ${user.uid}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating Aadhar verification: $e');
      }
      return false;
    }
  }
}

// Vendor auth result class
class VendorAuthResult {
  final bool isSuccess;
  final VendorModel? vendor;
  final String? message;

  VendorAuthResult._(this.isSuccess, this.vendor, this.message);

  factory VendorAuthResult.success(VendorModel? vendor, {String? message}) {
    return VendorAuthResult._(true, vendor, message);
  }

  factory VendorAuthResult.failure(String message) {
    return VendorAuthResult._(false, null, message);
  }
}

// Extension method for VendorModel
extension VendorModelExtension on VendorModel {
  VendorModel copyWith({
    String? uid,
    String? email,
    String? phoneNumber,
    String? displayName,
    String? photoURL,
    bool? isVerified,
    bool? aadhaarVerified,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? approvedAt,
    String? approvedBy,
    VendorProfile? profile,
    BusinessDetails? businessDetails,
    BankDetails? bankDetails,
    VendorDocuments? documents,
    double? rating,
    int? totalBookings,
    VendorStats? stats,
    VendorSettings? settings,
    List<String>? services,
    List<String>? portfolioImages,
    VerificationStatus? verificationStatus,
  }) {
    return VendorModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isVerified: isVerified ?? this.isVerified,
      aadhaarVerified: aadhaarVerified ?? this.aadhaarVerified,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      approvedBy: approvedBy ?? this.approvedBy,
      profile: profile ?? this.profile,
      businessDetails: businessDetails ?? this.businessDetails,
      bankDetails: bankDetails ?? this.bankDetails,
      documents: documents ?? this.documents,
      rating: rating ?? this.rating,
      totalBookings: totalBookings ?? this.totalBookings,
      stats: stats ?? this.stats,
      settings: settings ?? this.settings,
      services: services ?? this.services,
      portfolioImages: portfolioImages ?? this.portfolioImages,
      verificationStatus: verificationStatus ?? this.verificationStatus,
    );
  }
}
