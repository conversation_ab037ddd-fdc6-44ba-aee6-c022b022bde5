1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.event_vendor_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
7-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:5-74
8        android:minSdkVersion="23"
8-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:15-41
9        android:targetSdkVersion="35" />
9-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:42-71
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:40:5-45:15
24        <intent>
24-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:41:9-44:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-72
25-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:42:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
27-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:19-48
28        </intent>
29        <intent>
29-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
32-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
36-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
37    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
37-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
37-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
38    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
38-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
38-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
39    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
39-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
40    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
40-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
41    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
42    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
43    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
43-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
43-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
44    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
44-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c15fcb03cf7d589ff348edc8e1c2500\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
44-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c15fcb03cf7d589ff348edc8e1c2500\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
45
46    <permission
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
47        android:name="com.example.event_vendor_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.example.event_vendor_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
51
52    <application
53        android:name="android.app.Application"
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\4cf7f43b1fbc1e75f08fcb2008611c3d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:icon="@mipmap/ic_launcher"
58        android:label="Event Vendor" >
59        <activity
60            android:name="com.example.event_vendor_app.MainActivity"
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
62            android:exported="true"
63            android:hardwareAccelerated="true"
64            android:launchMode="singleTop"
65            android:taskAffinity=""
66            android:theme="@style/LaunchTheme"
67            android:windowSoftInputMode="adjustResize" >
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
76                android:name="io.flutter.embedding.android.NormalTheme"
77                android:resource="@style/NormalTheme" />
78
79            <intent-filter>
80                <action android:name="android.intent.action.MAIN" />
81
82                <category android:name="android.intent.category.LAUNCHER" />
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
90            android:name="flutterEmbedding"
91            android:value="2" />
92
93        <service
93-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:19
94            android:name="com.google.firebase.components.ComponentDiscoveryService"
94-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:18-89
95            android:directBootAware="true"
95-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
96            android:exported="false" >
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
97            <meta-data
97-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
98-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-128
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-82
100            <meta-data
100-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
101-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
103            <meta-data
103-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
104                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
104-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
106            <meta-data
106-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
107                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
107-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
109            <meta-data
109-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
110                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
110-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
112            <meta-data
112-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
113                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
113-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
115            <meta-data
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
116                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
118            <meta-data
118-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
119                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
119-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
121            <meta-data
121-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
122                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
122-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a2580479c64fa859ace56a2a9dfae21f\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
124            <meta-data
124-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
125                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
125-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
127            <meta-data
127-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
128-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
130            <meta-data
130-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
131                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
131-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
133            <meta-data
133-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
134                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
134-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\0fd2395212dff323962ae2d7e0856f52\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
136            <meta-data
136-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
137                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
137-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7543805328f75bd0e422a517d89e916d\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
139            <meta-data
139-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
140                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
140-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
142            <meta-data
142-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
143                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
143-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1252277ce00fd761f7f3deb9d67f629f\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
145            <meta-data
145-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
146                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
146-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
148            <meta-data
148-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
149                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
149-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6e9f468c85b86c080fd3f534314d97e\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
151            <meta-data
151-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c713f7918f16cfe302be5cb2164eee0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
152                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
152-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c713f7918f16cfe302be5cb2164eee0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c713f7918f16cfe302be5cb2164eee0\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
154            <meta-data
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
155                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
155-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
157            <meta-data
157-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\55fa9d2b8f4b567a1b4e074c6f0188d5\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
158                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
158-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\55fa9d2b8f4b567a1b4e074c6f0188d5\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\55fa9d2b8f4b567a1b4e074c6f0188d5\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
160        </service>
161        <service
161-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
162            android:name="com.baseflow.geolocator.GeolocatorLocationService"
162-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
163            android:enabled="true"
163-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
164            android:exported="false"
164-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
165            android:foregroundServiceType="location" />
165-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
166
167        <provider
167-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
168            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
168-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
169            android:authorities="com.example.event_vendor_app.flutter.image_provider"
169-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
170            android:exported="false"
170-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
171            android:grantUriPermissions="true" >
171-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
172            <meta-data
172-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
173                android:name="android.support.FILE_PROVIDER_PATHS"
173-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
174                android:resource="@xml/flutter_image_picker_file_paths" />
174-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
175        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
176        <service
176-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
177            android:name="com.google.android.gms.metadata.ModuleDependencies"
177-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
178            android:enabled="false"
178-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
179            android:exported="false" >
179-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
180            <intent-filter>
180-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
181                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
181-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
181-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
182            </intent-filter>
183
184            <meta-data
184-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
185                android:name="photopicker_activity:0:required"
185-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
186                android:value="" />
186-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
187        </service>
188
189        <activity
189-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
190            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
190-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
191            android:exported="false"
191-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
192            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
192-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
193
194        <service
194-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
195            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
195-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
196            android:exported="false"
196-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
197            android:permission="android.permission.BIND_JOB_SERVICE" />
197-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
198        <service
198-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
199            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
199-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
200            android:exported="false" >
200-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
201            <intent-filter>
201-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
202                <action android:name="com.google.firebase.MESSAGING_EVENT" />
202-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
202-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
203            </intent-filter>
204        </service>
205
206        <receiver
206-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
207            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
207-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
208            android:exported="true"
208-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
209            android:permission="com.google.android.c2dm.permission.SEND" >
209-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
210            <intent-filter>
210-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
211                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
211-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
211-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
212            </intent-filter>
213        </receiver>
214
215        <provider
215-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
216            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
216-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
217            android:authorities="com.example.event_vendor_app.flutterfirebasemessaginginitprovider"
217-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
218            android:exported="false"
218-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
219            android:initOrder="99" />
219-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
220
221        <activity
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
222            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
223            android:excludeFromRecents="true"
223-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
224            android:exported="true"
224-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
225            android:launchMode="singleTask"
225-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
226            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
226-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
227            <intent-filter>
227-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
228                <action android:name="android.intent.action.VIEW" />
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
228-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
229
230                <category android:name="android.intent.category.DEFAULT" />
230-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
230-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
231                <category android:name="android.intent.category.BROWSABLE" />
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
232
233                <data
233-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
234                    android:host="firebase.auth"
235                    android:path="/"
236                    android:scheme="genericidp" />
237            </intent-filter>
238        </activity>
239        <activity
239-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
240            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
240-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
241            android:excludeFromRecents="true"
241-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
242            android:exported="true"
242-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
243            android:launchMode="singleTask"
243-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
244            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
244-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
245            <intent-filter>
245-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
246                <action android:name="android.intent.action.VIEW" />
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
246-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
247
248                <category android:name="android.intent.category.DEFAULT" />
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
248-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
249                <category android:name="android.intent.category.BROWSABLE" />
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
249-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e8cc5c55bd7f8a11694c02c78c7d4e9\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
250
251                <data
251-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
252                    android:host="firebase.auth"
253                    android:path="/"
254                    android:scheme="recaptcha" />
255            </intent-filter>
256        </activity>
257
258        <uses-library
258-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
259            android:name="androidx.window.extensions"
259-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
260            android:required="false" />
260-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
261        <uses-library
261-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
262            android:name="androidx.window.sidecar"
262-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
263            android:required="false" />
263-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dade49c16e014f2a584d75ec417dcd95\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
264
265        <service
265-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
266            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
266-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
267            android:enabled="true"
267-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
268            android:exported="false" >
268-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
269            <meta-data
269-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
270                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
270-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
271                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
271-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
272        </service>
273
274        <activity
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
275            android:name="androidx.credentials.playservices.HiddenActivity"
275-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
276            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
276-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
277            android:enabled="true"
277-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
278            android:exported="false"
278-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
279            android:fitsSystemWindows="true"
279-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
280            android:theme="@style/Theme.Hidden" >
280-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\7a68495870855c1c1533ec431e49fed0\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
281        </activity>
282        <activity
282-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
283            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
283-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
284            android:excludeFromRecents="true"
284-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
285            android:exported="false"
285-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
286            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
286-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
287        <!--
288            Service handling Google Sign-In user revocation. For apps that do not integrate with
289            Google Sign-In, this service will never be started.
290        -->
291        <service
291-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
292            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
292-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
293            android:exported="true"
293-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
294            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
294-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
295            android:visibleToInstantApps="true" />
295-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8044441bbc07597e7947bc8c9053130\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
296
297        <receiver
297-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
298            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
298-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
299            android:exported="true"
299-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
300            android:permission="com.google.android.c2dm.permission.SEND" >
300-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
301            <intent-filter>
301-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
302                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
302-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
302-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
303            </intent-filter>
304
305            <meta-data
305-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
306                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
306-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
307                android:value="true" />
307-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
308        </receiver>
309        <!--
310             FirebaseMessagingService performs security checks at runtime,
311             but set to not exported to explicitly avoid allowing another app to call it.
312        -->
313        <service
313-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
314            android:name="com.google.firebase.messaging.FirebaseMessagingService"
314-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
315            android:directBootAware="true"
315-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
316            android:exported="false" >
316-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a2b9992d36523052d9bac95a4a2f5a63\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
317            <intent-filter android:priority="-500" >
317-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
318                <action android:name="com.google.firebase.MESSAGING_EVENT" />
318-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
318-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
319            </intent-filter>
320        </service>
321
322        <provider
322-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
323            android:name="com.google.firebase.provider.FirebaseInitProvider"
323-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
324            android:authorities="com.example.event_vendor_app.firebaseinitprovider"
324-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
325            android:directBootAware="true"
325-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
326            android:exported="false"
326-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
327            android:initOrder="100" />
327-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80a6420684a8e74c087651d6748b432e\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
328
329        <receiver
329-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
330            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
330-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
331            android:enabled="true"
331-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
332            android:exported="false" >
332-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
333        </receiver>
334
335        <service
335-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
336            android:name="com.google.android.gms.measurement.AppMeasurementService"
336-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
337            android:enabled="true"
337-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
338            android:exported="false" />
338-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
339        <service
339-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
340            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
340-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
341            android:enabled="true"
341-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
342            android:exported="false"
342-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
343            android:permission="android.permission.BIND_JOB_SERVICE" />
343-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\30b9c90b1657b01fd7223f17f2fde3f1\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
344
345        <provider
345-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
346            android:name="androidx.startup.InitializationProvider"
346-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
347            android:authorities="com.example.event_vendor_app.androidx-startup"
347-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
348            android:exported="false" >
348-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
349            <meta-data
349-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
350                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
350-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
351                android:value="androidx.startup" />
351-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2c3d385343802aa5d742415bdcf14f1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
352            <meta-data
352-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
353                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
353-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
354                android:value="androidx.startup" />
354-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
355        </provider>
356
357        <uses-library
357-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\7bb4227349f335858bec6b2869fd0c97\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
358            android:name="android.ext.adservices"
358-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\7bb4227349f335858bec6b2869fd0c97\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
359            android:required="false" />
359-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\7bb4227349f335858bec6b2869fd0c97\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
360
361        <activity
361-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d471c06edec8a15dc970c0328249b4a2\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
362            android:name="com.google.android.gms.common.api.GoogleApiActivity"
362-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d471c06edec8a15dc970c0328249b4a2\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
363            android:exported="false"
363-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d471c06edec8a15dc970c0328249b4a2\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
364            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
364-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\d471c06edec8a15dc970c0328249b4a2\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
365
366        <meta-data
366-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\72243b3865f1bb5f8322680f1038a2ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
367            android:name="com.google.android.gms.version"
367-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\72243b3865f1bb5f8322680f1038a2ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
368            android:value="@integer/google_play_services_version" />
368-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\72243b3865f1bb5f8322680f1038a2ab\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
369
370        <receiver
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
371            android:name="androidx.profileinstaller.ProfileInstallReceiver"
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
372            android:directBootAware="false"
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
373            android:enabled="true"
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
374            android:exported="true"
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
375            android:permission="android.permission.DUMP" >
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
376            <intent-filter>
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
377                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
378            </intent-filter>
379            <intent-filter>
379-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
380                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
381            </intent-filter>
382            <intent-filter>
382-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
383                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
384            </intent-filter>
385            <intent-filter>
385-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
386                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\67d7b4cbdf827d5a8789720e50b16b9e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
387            </intent-filter>
388        </receiver>
389
390        <service
390-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
391            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
391-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
392            android:exported="false" >
392-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
393            <meta-data
393-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
394                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
394-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
395                android:value="cct" />
395-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\ad33ca5d24911cac0e686b99b569aaa7\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
396        </service>
397        <service
397-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
398            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
398-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
399            android:exported="false"
399-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
400            android:permission="android.permission.BIND_JOB_SERVICE" >
400-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
401        </service>
402
403        <receiver
403-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
404            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
404-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
405            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
405-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\a9ac66344f7b1e32c1f2d20b3caa127e\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
406        <activity
406-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
407            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
407-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
408            android:exported="false"
408-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
409            android:stateNotNeeded="true"
409-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
410            android:theme="@style/Theme.PlayCore.Transparent" />
410-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\74e443eb8a268b3cc6a9da53217a98c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
411    </application>
412
413</manifest>
