import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../config/app_theme.dart';
import '../../models/vendor_model.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<VendorAuthProvider>(context);
    final vendor = authProvider.currentVendor;

    if (vendor == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Image.asset(
                  'assets/logos/logo.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
            const SizedBox(width: 12),
            const Text('Profile'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditProfileDialog(vendor),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withOpacity(0.8),
                  ],
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Profile Picture
                    Stack(
                      children: [
                        CircleAvatar(
                          radius: 60,
                          backgroundColor: Colors.white,
                          child: vendor.photoURL != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(60),
                                  child: Image.network(
                                    vendor.photoURL!,
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : Icon(
                                  Icons.person,
                                  size: 60,
                                  color: AppTheme.primaryColor,
                                ),
                        ),
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: Icon(Icons.camera_alt,
                                  color: AppTheme.primaryColor),
                              onPressed: () => _changeProfilePicture(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Name and Business
                    Text(
                      vendor.profile.businessName.isNotEmpty
                          ? vendor.profile.businessName
                          : 'Business Name',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${vendor.profile.firstName} ${vendor.profile.lastName}',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Verification Status
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildVerificationChip(
                          'Email',
                          vendor.isVerified,
                          Icons.email,
                        ),
                        const SizedBox(width: 8),
                        _buildVerificationChip(
                          'Aadhar',
                          vendor.aadhaarVerified,
                          Icons.credit_card,
                        ),
                        const SizedBox(width: 8),
                        _buildVerificationChip(
                          'Approved',
                          vendor.isApproved,
                          Icons.verified,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Profile Sections
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Personal Information
                  _buildSection(
                    'Personal Information',
                    Icons.person,
                    [
                      _buildInfoTile('Full Name',
                          '${vendor.profile.firstName} ${vendor.profile.lastName}'),
                      _buildInfoTile('Email', vendor.email),
                      _buildInfoTile(
                          'Phone', vendor.phoneNumber ?? 'Not provided'),
                      _buildInfoTile(
                          'Display Name', vendor.displayName ?? 'Not provided'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Business Information
                  _buildSection(
                    'Business Information',
                    Icons.business,
                    [
                      _buildInfoTile(
                          'Business Name', vendor.profile.businessName),
                      _buildInfoTile(
                          'Description',
                          vendor.profile.description.isNotEmpty
                              ? vendor.profile.description
                              : 'Not provided'),
                      _buildInfoTile(
                          'License Number',
                          vendor.businessDetails.licenseNumber.isNotEmpty
                              ? vendor.businessDetails.licenseNumber
                              : 'Not provided'),
                      _buildInfoTile('Established Year',
                          vendor.businessDetails.establishedYear.toString()),
                      _buildInfoTile('Employee Count',
                          vendor.businessDetails.employeeCount.toString()),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Address Information
                  _buildSection(
                    'Address',
                    Icons.location_on,
                    [
                      _buildInfoTile(
                          'Street',
                          vendor.businessDetails.businessAddress.street
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.street
                              : 'Not provided'),
                      _buildInfoTile(
                          'City',
                          vendor.businessDetails.businessAddress.city.isNotEmpty
                              ? vendor.businessDetails.businessAddress.city
                              : 'Not provided'),
                      _buildInfoTile(
                          'State',
                          vendor.businessDetails.businessAddress.state
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.state
                              : 'Not provided'),
                      _buildInfoTile('Country',
                          vendor.businessDetails.businessAddress.country),
                      _buildInfoTile(
                          'Pincode',
                          vendor.businessDetails.businessAddress.pincode
                                  .isNotEmpty
                              ? vendor.businessDetails.businessAddress.pincode
                              : 'Not provided'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Services & Specializations
                  _buildSection(
                    'Services & Specializations',
                    Icons.star,
                    [
                      _buildChipsTile(
                          'Service Areas', vendor.profile.serviceAreas),
                      _buildChipsTile(
                          'Specializations', vendor.profile.specializations),
                      _buildChipsTile('Event Types', vendor.profile.eventTypes),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Statistics
                  _buildSection(
                    'Statistics',
                    Icons.analytics,
                    [
                      _buildInfoTile(
                          'Total Events', vendor.stats.totalEvents.toString()),
                      _buildInfoTile('Completed Events',
                          vendor.stats.completedEvents.toString()),
                      _buildInfoTile('Average Rating',
                          vendor.stats.averageRating.toStringAsFixed(1)),
                      _buildInfoTile('Total Reviews',
                          vendor.stats.totalReviews.toString()),
                      _buildInfoTile('Total Earnings',
                          '₹${vendor.stats.totalEarnings.toStringAsFixed(0)}'),
                      _buildInfoTile('Completion Rate',
                          '${vendor.stats.completionRate.toStringAsFixed(1)}%'),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Account Actions
                  _buildSection(
                    'Account Actions',
                    Icons.settings,
                    [
                      _buildActionTile('Change Password', Icons.lock,
                          () => _changePassword()),
                      _buildActionTile('Notification Settings',
                          Icons.notifications, () => _notificationSettings()),
                      _buildActionTile('Privacy Settings', Icons.privacy_tip,
                          () => _privacySettings()),
                      _buildActionTile(
                          'Help & Support', Icons.help, () => _helpSupport()),
                      _buildActionTile(
                          'Sign Out', Icons.logout, () => _signOut(),
                          isDestructive: true),
                    ],
                  ),

                  const SizedBox(height: 32),

                  // App Version
                  Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationChip(String label, bool isVerified, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isVerified
            ? Colors.green.withOpacity(0.2)
            : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isVerified ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isVerified ? Icons.check_circle : Icons.cancel,
            size: 14,
            color: isVerified ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: isVerified ? Colors.green : Colors.red,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChipsTile(String label, List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 8),
          items.isEmpty
              ? const Text('Not specified',
                  style: TextStyle(color: Colors.grey))
              : Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: items
                      .map((item) => Chip(
                            label: Text(item,
                                style: const TextStyle(fontSize: 12)),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ))
                      .toList(),
                ),
        ],
      ),
    );
  }

  Widget _buildActionTile(String title, IconData icon, VoidCallback onTap,
      {bool isDestructive = false}) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : AppTheme.primaryColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : null,
        ),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showEditProfileDialog(VendorModel vendor) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const EditProfileScreen(),
      ),
    );
  }

  void _changeProfilePicture() {
    // TODO: Implement profile picture change
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change profile picture feature coming soon!'),
      ),
    );
  }

  void _changePassword() {
    // TODO: Implement change password
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Change password feature coming soon!'),
      ),
    );
  }

  void _notificationSettings() {
    // TODO: Implement notification settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notification settings feature coming soon!'),
      ),
    );
  }

  void _privacySettings() {
    // TODO: Implement privacy settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Privacy settings feature coming soon!'),
      ),
    );
  }

  void _helpSupport() {
    // TODO: Implement help & support
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Help & support feature coming soon!'),
      ),
    );
  }

  void _signOut() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final authProvider =
                  Provider.of<VendorAuthProvider>(context, listen: false);
              await authProvider.signOut();
              if (mounted) {
                Navigator.of(context).pop();
                // Navigate to login screen
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                const Text('Sign Out', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
