{"logs": [{"outputFile": "com.example.event_vendor_app-mergeDebugResources-51:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\591da1a48a958d442215c4093bb458e6\\transformed\\appcompat-1.1.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,901,992,1084,1185,1279,1380,1474,1569,1668,1759,1850,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "207,313,424,515,620,742,820,896,987,1079,1180,1274,1375,1469,1564,1663,1754,1845,1926,2035,2139,2238,2350,2462,2583,2748,2849,2931"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,901,992,1084,1185,1279,1380,1474,1569,1668,1759,1850,1931,2040,2144,2243,2355,2467,2588,2753,6994", "endColumns": "106,105,110,90,104,121,77,75,90,91,100,93,100,93,94,98,90,90,80,108,103,98,111,111,120,164,100,81", "endOffsets": "207,313,424,515,620,742,820,896,987,1079,1180,1274,1375,1469,1564,1663,1754,1845,1926,2035,2139,2238,2350,2462,2583,2748,2849,7071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d68f6fd17619c38bcf250daf71ab5ef6\\transformed\\browser-1.8.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6251,6449,6552,6663", "endColumns": "108,102,110,103", "endOffsets": "6355,6547,6658,6762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d6db1a707cede35414abff3851a56c18\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3081,3183,3286,3388,3492,3595,3696,7076", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3178,3281,3383,3487,3590,3691,3813,7172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d4185390180950757c3d08502d2fd14b\\transformed\\preference-1.2.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,266,352,493,662,744", "endColumns": "71,88,85,140,168,81,75", "endOffsets": "172,261,347,488,657,739,815"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6179,6360,6767,6853,7177,7346,7428", "endColumns": "71,88,85,140,168,81,75", "endOffsets": "6246,6444,6848,6989,7341,7423,7499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\77b0af5e1299779a3a9698a95990dce8\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,113", "endOffsets": "163,277"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2854,2967", "endColumns": "112,113", "endOffsets": "2962,3076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b5eb4bf95253b88a2aad39e0426e67c\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3818,3937,4116,4255,4376,4540,4665,4770,5036,5218,5334,5508,5644,5793,5954,6018,6087", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "3932,4111,4250,4371,4535,4660,4765,4871,5213,5329,5503,5639,5788,5949,6013,6082,6174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a12066cd0b0b35d6deebf50936ee2b0f\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4876", "endColumns": "159", "endOffsets": "5031"}}]}]}