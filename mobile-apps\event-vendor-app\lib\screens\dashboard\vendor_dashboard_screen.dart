import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../config/app_theme.dart';
import '../events/events_screen.dart';
import '../tasks/tasks_screen.dart';
import '../employees/team_screen.dart';
import '../profile/profile_screen.dart';
import '../../services/firebase_test_service.dart';
import '../debug/event_vendor_debug_screen.dart';

class VendorDashboardScreen extends StatefulWidget {
  const VendorDashboardScreen({super.key});

  @override
  State<VendorDashboardScreen> createState() => _VendorDashboardScreenState();
}

class _VendorDashboardScreenState extends State<VendorDashboardScreen> {
  int _currentIndex = 0;
  final FirebaseTestService _firebaseTestService = FirebaseTestService();

  final List<Widget> _screens = [
    const DashboardTabScreen(),
    const EventsScreen(),
    const TasksScreen(),
    const TeamScreen(),
    const ProfileScreen(),
  ];

  /// Test Firebase connection
  Future<void> _testFirebaseConnection() async {
    try {
      if (kDebugMode) {
        print('🔥 Testing Firebase connection...');
      }

      final results = await _firebaseTestService.runComprehensiveTest();

      if (mounted) {
        final isSuccess = results['overall']['allConnected'] == true;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(results['overall']['message']),
            backgroundColor: isSuccess ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Details',
              textColor: Colors.white,
              onPressed: () {
                _showFirebaseTestResults(results);
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Firebase test failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// Show detailed Firebase test results
  void _showFirebaseTestResults(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Firebase Test Results'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Overall Status: ${results['overall']['status']}'),
              const SizedBox(height: 16),
              const Text('Service Tests:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              ...results['tests'].entries.map((entry) {
                final test = entry.value;
                return ListTile(
                  leading: Icon(
                    test['connected'] ? Icons.check_circle : Icons.error,
                    color: test['connected'] ? Colors.green : Colors.red,
                  ),
                  title: Text(entry.key.toUpperCase()),
                  subtitle: Text(test['message']),
                );
              }).toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.event),
            label: 'Events',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.task),
            label: 'Tasks',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Team',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}

class DashboardTabScreen extends StatelessWidget {
  const DashboardTabScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Image.asset(
                  'assets/logos/logo.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
            const SizedBox(width: 12),
            const Text('Event Vendor'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (kDebugMode) ...[
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EventVendorDebugScreen(),
                  ),
                );
              },
              tooltip: 'Debug & Recovery',
            ),
          ],
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
        ],
      ),
      body: Consumer<VendorAuthProvider>(
        builder: (context, authProvider, child) {
          final vendor = authProvider.currentVendor;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.spacingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.spacingL),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(AppTheme.radiusL),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome back,',
                        style:
                            AppTheme.bodyLarge.copyWith(color: Colors.white70),
                      ),
                      const SizedBox(height: AppTheme.spacingXS),
                      Text(
                        vendor?.profile.businessName ?? 'Vendor',
                        style: AppTheme.headingMedium
                            .copyWith(color: Colors.white),
                      ),
                      const SizedBox(height: AppTheme.spacingS),
                      Text(
                        'Manage your events and team efficiently',
                        style:
                            AppTheme.bodyMedium.copyWith(color: Colors.white70),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppTheme.spacingXL),

                // Quick Stats
                const Text(
                  'Quick Stats',
                  style: AppTheme.headingSmall,
                ),
                const SizedBox(height: AppTheme.spacingM),

                Row(
                  children: [
                    Expanded(
                      child: _StatCard(
                        icon: Icons.event,
                        title: 'Active Events',
                        value: '${vendor?.stats.totalEvents ?? 0}',
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingM),
                    Expanded(
                      child: _StatCard(
                        icon: Icons.task,
                        title: 'Pending Tasks',
                        value: '${vendor?.stats.totalEvents ?? 0}',
                        color: AppTheme.warningColor,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingM),

                Row(
                  children: [
                    Expanded(
                      child: _StatCard(
                        icon: Icons.people,
                        title: 'Team Members',
                        value: '${vendor?.stats.activeEmployees ?? 0}',
                        color: AppTheme.accentColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingM),
                    Expanded(
                      child: _StatCard(
                        icon: Icons.star,
                        title: 'Rating',
                        value: vendor?.rating.toStringAsFixed(1) ?? '0.0',
                        color: AppTheme.successColor,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.spacingXL),

                // Recent Activities
                const Text(
                  'Recent Activities',
                  style: AppTheme.headingSmall,
                ),
                const SizedBox(height: AppTheme.spacingM),

                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppTheme.spacingL),
                  decoration: BoxDecoration(
                    color: AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusL),
                    border: Border.all(color: AppTheme.dividerColor),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.history,
                        size: 48,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(height: AppTheme.spacingM),
                      Text(
                        'No recent activities',
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingS),
                      Text(
                        'Your recent activities will appear here',
                        style: AppTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final Color color;

  const _StatCard({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingM),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingS),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusM),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: AppTheme.spacingM),
          Text(
            value,
            style: AppTheme.headingMedium.copyWith(
              color: color,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXS),
          Text(
            title,
            style: AppTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

// Placeholder screens for other tabs
class EventsTabScreen extends StatelessWidget {
  const EventsTabScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Image.asset(
                  'assets/logos/logo.png',
                  fit: BoxFit.contain,
                ),
              ),
            ),
            const SizedBox(width: 12),
            const Text('Events'),
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Text('Events Screen - Coming Soon!'),
      ),
    );
  }
}
