import 'package:cloud_firestore/cloud_firestore.dart';

class VendorModel {
  final String uid;
  final String email;
  final String? phoneNumber;
  final String? displayName;
  final String? photoURL;
  final bool isVerified;
  final bool aadhaarVerified;
  final bool isApproved;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? approvedAt;
  final String? approvedBy;
  final VendorProfile profile;
  final BusinessDetails businessDetails;
  final BankDetails? bankDetails;
  final VendorDocuments documents;
  final double rating;
  final int totalBookings;
  final VendorStats stats;
  // Additional properties needed for profile service
  final VendorSettings? settings;
  final List<String> services;
  final List<String> portfolioImages;
  final VerificationStatus verificationStatus;

  VendorModel({
    required this.uid,
    required this.email,
    this.phoneNumber,
    this.displayName,
    this.photoURL,
    required this.isVerified,
    required this.aadhaarVerified,
    required this.isApproved,
    required this.createdAt,
    required this.updatedAt,
    this.approvedAt,
    this.approvedBy,
    required this.profile,
    required this.businessDetails,
    this.bankDetails,
    required this.documents,
    required this.rating,
    required this.totalBookings,
    required this.stats,
    this.settings,
    required this.services,
    required this.portfolioImages,
    required this.verificationStatus,
  });

  factory VendorModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

    return VendorModel(
      uid: doc.id,
      email: data['email'] ?? '',
      phoneNumber: data['phoneNumber'],
      displayName: data['displayName'],
      photoURL: data['photoURL'],
      isVerified: data['isVerified'] ?? false,
      aadhaarVerified: data['aadhaarVerified'] ?? false,
      isApproved: data['isApproved'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      approvedAt: data['approvedAt'] != null
          ? (data['approvedAt'] as Timestamp).toDate()
          : null,
      approvedBy: data['approvedBy'],
      profile: VendorProfile.fromMap(data['profile'] ?? {}),
      businessDetails: BusinessDetails.fromMap(data['businessDetails'] ?? {}),
      bankDetails: data['bankDetails'] != null
          ? BankDetails.fromMap(data['bankDetails'])
          : null,
      documents: VendorDocuments.fromMap(data['documents'] ?? {}),
      rating: (data['rating'] ?? 0.0).toDouble(),
      totalBookings: data['totalBookings'] ?? 0,
      stats: VendorStats.fromMap(data['stats'] ?? {}),
      settings: data['settings'] != null
          ? VendorSettings.fromMap(data['settings'])
          : null,
      services: List<String>.from(data['services'] ?? []),
      portfolioImages: List<String>.from(data['portfolioImages'] ?? []),
      verificationStatus:
          VerificationStatus.fromMap(data['verificationStatus'] ?? {}),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'phoneNumber': phoneNumber,
      'displayName': displayName,
      'photoURL': photoURL,
      'isVerified': isVerified,
      'aadhaarVerified': aadhaarVerified,
      'isApproved': isApproved,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
      'approvedBy': approvedBy,
      'profile': profile.toMap(),
      'businessDetails': businessDetails.toMap(),
      'bankDetails': bankDetails?.toMap(),
      'documents': documents.toMap(),
      'rating': rating,
      'totalBookings': totalBookings,
      'stats': stats.toMap(),
      'settings': settings?.toMap(),
      'services': services,
      'portfolioImages': portfolioImages,
      'verificationStatus': verificationStatus.toMap(),
    };
  }

  VendorModel copyWith({
    String? uid,
    String? email,
    String? phoneNumber,
    String? displayName,
    String? photoURL,
    bool? isVerified,
    bool? aadhaarVerified,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? approvedAt,
    String? approvedBy,
    VendorProfile? profile,
    BusinessDetails? businessDetails,
    BankDetails? bankDetails,
    VendorDocuments? documents,
    double? rating,
    int? totalBookings,
    VendorStats? stats,
    VendorSettings? settings,
    List<String>? services,
    List<String>? portfolioImages,
    VerificationStatus? verificationStatus,
  }) {
    return VendorModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      isVerified: isVerified ?? this.isVerified,
      aadhaarVerified: aadhaarVerified ?? this.aadhaarVerified,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      approvedBy: approvedBy ?? this.approvedBy,
      profile: profile ?? this.profile,
      businessDetails: businessDetails ?? this.businessDetails,
      bankDetails: bankDetails ?? this.bankDetails,
      documents: documents ?? this.documents,
      rating: rating ?? this.rating,
      totalBookings: totalBookings ?? this.totalBookings,
      stats: stats ?? this.stats,
      settings: settings ?? this.settings,
      services: services ?? this.services,
      portfolioImages: portfolioImages ?? this.portfolioImages,
      verificationStatus: verificationStatus ?? this.verificationStatus,
    );
  }
}

class VendorProfile {
  final String businessName;
  final String firstName;
  final String lastName;
  final String description;
  final String? website;
  final SocialMedia? socialMedia;
  final List<String> serviceAreas;
  final List<String> specializations;
  final List<String> eventTypes;
  // Additional properties needed for edit profile
  final String address;
  final String city;
  final String state;
  final String pincode;
  final String name; // Combined name for compatibility

  VendorProfile({
    required this.businessName,
    required this.firstName,
    required this.lastName,
    required this.description,
    this.website,
    this.socialMedia,
    required this.serviceAreas,
    required this.specializations,
    required this.eventTypes,
    required this.address,
    required this.city,
    required this.state,
    required this.pincode,
    required this.name,
  });

  factory VendorProfile.fromMap(Map<String, dynamic> map) {
    return VendorProfile(
      businessName: map['businessName'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      description: map['description'] ?? '',
      website: map['website'],
      socialMedia: map['socialMedia'] != null
          ? SocialMedia.fromMap(map['socialMedia'])
          : null,
      serviceAreas: List<String>.from(map['serviceAreas'] ?? []),
      specializations: List<String>.from(map['specializations'] ?? []),
      eventTypes: List<String>.from(map['eventTypes'] ?? []),
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      pincode: map['pincode'] ?? '',
      name: map['name'] ??
          '${map['firstName'] ?? ''} ${map['lastName'] ?? ''}'.trim(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'businessName': businessName,
      'firstName': firstName,
      'lastName': lastName,
      'description': description,
      'website': website,
      'socialMedia': socialMedia?.toMap(),
      'serviceAreas': serviceAreas,
      'specializations': specializations,
      'eventTypes': eventTypes,
      'address': address,
      'city': city,
      'state': state,
      'pincode': pincode,
      'name': name,
    };
  }

  String get fullName => '$firstName $lastName';

  VendorProfile copyWith({
    String? businessName,
    String? firstName,
    String? lastName,
    String? description,
    String? website,
    SocialMedia? socialMedia,
    List<String>? serviceAreas,
    List<String>? specializations,
    List<String>? eventTypes,
    String? address,
    String? city,
    String? state,
    String? pincode,
    String? name,
  }) {
    return VendorProfile(
      businessName: businessName ?? this.businessName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      description: description ?? this.description,
      website: website ?? this.website,
      socialMedia: socialMedia ?? this.socialMedia,
      serviceAreas: serviceAreas ?? this.serviceAreas,
      specializations: specializations ?? this.specializations,
      eventTypes: eventTypes ?? this.eventTypes,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      name: name ?? this.name,
    );
  }
}

class SocialMedia {
  final String? facebook;
  final String? instagram;
  final String? twitter;
  final String? linkedin;

  SocialMedia({
    this.facebook,
    this.instagram,
    this.twitter,
    this.linkedin,
  });

  factory SocialMedia.fromMap(Map<String, dynamic> map) {
    return SocialMedia(
      facebook: map['facebook'],
      instagram: map['instagram'],
      twitter: map['twitter'],
      linkedin: map['linkedin'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'facebook': facebook,
      'instagram': instagram,
      'twitter': twitter,
      'linkedin': linkedin,
    };
  }
}

class BusinessDetails {
  final String? registrationNumber;
  final String? gstNumber;
  final String? panNumber;
  final String licenseNumber;
  final int establishedYear;
  final int employeeCount;
  final Address businessAddress;
  final Map<String, OperatingHours> operatingHours;
  // Additional properties needed for edit profile
  final String businessType;
  final double annualRevenue;

  BusinessDetails({
    this.registrationNumber,
    this.gstNumber,
    this.panNumber,
    required this.licenseNumber,
    required this.establishedYear,
    required this.employeeCount,
    required this.businessAddress,
    required this.operatingHours,
    required this.businessType,
    required this.annualRevenue,
  });

  factory BusinessDetails.fromMap(Map<String, dynamic> map) {
    Map<String, OperatingHours> hours = {};
    if (map['operatingHours'] != null) {
      (map['operatingHours'] as Map<String, dynamic>).forEach((key, value) {
        hours[key] = OperatingHours.fromMap(value);
      });
    }

    return BusinessDetails(
      registrationNumber: map['registrationNumber'],
      gstNumber: map['gstNumber'],
      panNumber: map['panNumber'],
      licenseNumber: map['licenseNumber'] ?? '',
      establishedYear: map['establishedYear'] ?? DateTime.now().year,
      employeeCount: map['employeeCount'] ?? 0,
      businessAddress: Address.fromMap(map['businessAddress'] ?? {}),
      operatingHours: hours,
      businessType: map['businessType'] ?? '',
      annualRevenue: (map['annualRevenue'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> hours = {};
    operatingHours.forEach((key, value) {
      hours[key] = value.toMap();
    });

    return {
      'registrationNumber': registrationNumber,
      'gstNumber': gstNumber,
      'panNumber': panNumber,
      'licenseNumber': licenseNumber,
      'establishedYear': establishedYear,
      'employeeCount': employeeCount,
      'businessAddress': businessAddress.toMap(),
      'operatingHours': hours,
      'businessType': businessType,
      'annualRevenue': annualRevenue,
    };
  }

  BusinessDetails copyWith({
    String? registrationNumber,
    String? gstNumber,
    String? panNumber,
    String? licenseNumber,
    int? establishedYear,
    int? employeeCount,
    Address? businessAddress,
    Map<String, OperatingHours>? operatingHours,
    String? businessType,
    double? annualRevenue,
  }) {
    return BusinessDetails(
      registrationNumber: registrationNumber ?? this.registrationNumber,
      gstNumber: gstNumber ?? this.gstNumber,
      panNumber: panNumber ?? this.panNumber,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      establishedYear: establishedYear ?? this.establishedYear,
      employeeCount: employeeCount ?? this.employeeCount,
      businessAddress: businessAddress ?? this.businessAddress,
      operatingHours: operatingHours ?? this.operatingHours,
      businessType: businessType ?? this.businessType,
      annualRevenue: annualRevenue ?? this.annualRevenue,
    );
  }
}

class Address {
  final String street;
  final String city;
  final String state;
  final String country;
  final String pincode;
  final Coordinates? coordinates;

  Address({
    required this.street,
    required this.city,
    required this.state,
    required this.country,
    required this.pincode,
    this.coordinates,
  });

  factory Address.fromMap(Map<String, dynamic> map) {
    return Address(
      street: map['street'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      country: map['country'] ?? '',
      pincode: map['pincode'] ?? '',
      coordinates: map['coordinates'] != null
          ? Coordinates.fromMap(map['coordinates'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'street': street,
      'city': city,
      'state': state,
      'country': country,
      'pincode': pincode,
      'coordinates': coordinates?.toMap(),
    };
  }

  String get fullAddress => '$street, $city, $state $pincode, $country';
}

class Coordinates {
  final double latitude;
  final double longitude;

  Coordinates({
    required this.latitude,
    required this.longitude,
  });

  factory Coordinates.fromMap(Map<String, dynamic> map) {
    return Coordinates(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class OperatingHours {
  final String open;
  final String close;
  final bool isOpen;

  OperatingHours({
    required this.open,
    required this.close,
    required this.isOpen,
  });

  factory OperatingHours.fromMap(Map<String, dynamic> map) {
    return OperatingHours(
      open: map['open'] ?? '09:00',
      close: map['close'] ?? '18:00',
      isOpen: map['isOpen'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'open': open,
      'close': close,
      'isOpen': isOpen,
    };
  }
}

class BankDetails {
  final String accountHolderName;
  final String accountNumber;
  final String ifscCode;
  final String bankName;
  final String branchName;
  final String accountType;

  BankDetails({
    required this.accountHolderName,
    required this.accountNumber,
    required this.ifscCode,
    required this.bankName,
    required this.branchName,
    required this.accountType,
  });

  factory BankDetails.fromMap(Map<String, dynamic> map) {
    return BankDetails(
      accountHolderName: map['accountHolderName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      ifscCode: map['ifscCode'] ?? '',
      bankName: map['bankName'] ?? '',
      branchName: map['branchName'] ?? '',
      accountType: map['accountType'] ?? 'savings',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accountHolderName': accountHolderName,
      'accountNumber': accountNumber,
      'ifscCode': ifscCode,
      'bankName': bankName,
      'branchName': branchName,
      'accountType': accountType,
    };
  }
}

class VendorDocuments {
  final String aadhaarCard;
  final String? panCard;
  final String businessLicense;
  final String? gstCertificate;
  final String bankPassbook;
  final List<String> photos;

  VendorDocuments({
    required this.aadhaarCard,
    this.panCard,
    required this.businessLicense,
    this.gstCertificate,
    required this.bankPassbook,
    required this.photos,
  });

  factory VendorDocuments.fromMap(Map<String, dynamic> map) {
    return VendorDocuments(
      aadhaarCard: map['aadhaarCard'] ?? '',
      panCard: map['panCard'],
      businessLicense: map['businessLicense'] ?? '',
      gstCertificate: map['gstCertificate'],
      bankPassbook: map['bankPassbook'] ?? '',
      photos: List<String>.from(map['photos'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'aadhaarCard': aadhaarCard,
      'panCard': panCard,
      'businessLicense': businessLicense,
      'gstCertificate': gstCertificate,
      'bankPassbook': bankPassbook,
      'photos': photos,
    };
  }
}

class VendorStats {
  final int totalEvents;
  final int completedEvents;
  final int cancelledEvents;
  final double averageRating;
  final int totalReviews;
  final double totalEarnings;
  final double pendingCommissions;
  final int activeEmployees;
  final double completionRate;

  VendorStats({
    required this.totalEvents,
    required this.completedEvents,
    required this.cancelledEvents,
    required this.averageRating,
    required this.totalReviews,
    required this.totalEarnings,
    required this.pendingCommissions,
    required this.activeEmployees,
    required this.completionRate,
  });

  factory VendorStats.fromMap(Map<String, dynamic> map) {
    return VendorStats(
      totalEvents: map['totalEvents'] ?? 0,
      completedEvents: map['completedEvents'] ?? 0,
      cancelledEvents: map['cancelledEvents'] ?? 0,
      averageRating: (map['averageRating'] ?? 0.0).toDouble(),
      totalReviews: map['totalReviews'] ?? 0,
      totalEarnings: (map['totalEarnings'] ?? 0.0).toDouble(),
      pendingCommissions: (map['pendingCommissions'] ?? 0.0).toDouble(),
      activeEmployees: map['activeEmployees'] ?? 0,
      completionRate: (map['completionRate'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalEvents': totalEvents,
      'completedEvents': completedEvents,
      'cancelledEvents': cancelledEvents,
      'averageRating': averageRating,
      'totalReviews': totalReviews,
      'totalEarnings': totalEarnings,
      'pendingCommissions': pendingCommissions,
      'activeEmployees': activeEmployees,
      'completionRate': completionRate,
    };
  }
}

class VendorSettings {
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final bool marketingEmails;
  final String language;
  final String timezone;
  final bool profileVisibility;
  final bool autoAcceptBookings;

  VendorSettings({
    required this.emailNotifications,
    required this.smsNotifications,
    required this.pushNotifications,
    required this.marketingEmails,
    required this.language,
    required this.timezone,
    required this.profileVisibility,
    required this.autoAcceptBookings,
  });

  factory VendorSettings.fromMap(Map<String, dynamic> map) {
    return VendorSettings(
      emailNotifications: map['emailNotifications'] ?? true,
      smsNotifications: map['smsNotifications'] ?? true,
      pushNotifications: map['pushNotifications'] ?? true,
      marketingEmails: map['marketingEmails'] ?? false,
      language: map['language'] ?? 'en',
      timezone: map['timezone'] ?? 'Asia/Kolkata',
      profileVisibility: map['profileVisibility'] ?? true,
      autoAcceptBookings: map['autoAcceptBookings'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'pushNotifications': pushNotifications,
      'marketingEmails': marketingEmails,
      'language': language,
      'timezone': timezone,
      'profileVisibility': profileVisibility,
      'autoAcceptBookings': autoAcceptBookings,
    };
  }
}

class VerificationStatus {
  final bool emailVerified;
  final bool phoneVerified;
  final bool aadhaarVerified;
  final bool businessVerified;
  final bool bankVerified;
  final DateTime? emailVerifiedAt;
  final DateTime? phoneVerifiedAt;
  final DateTime? aadhaarVerifiedAt;
  final DateTime? businessVerifiedAt;
  final DateTime? bankVerifiedAt;

  VerificationStatus({
    required this.emailVerified,
    required this.phoneVerified,
    required this.aadhaarVerified,
    required this.businessVerified,
    required this.bankVerified,
    this.emailVerifiedAt,
    this.phoneVerifiedAt,
    this.aadhaarVerifiedAt,
    this.businessVerifiedAt,
    this.bankVerifiedAt,
  });

  factory VerificationStatus.fromMap(Map<String, dynamic> map) {
    return VerificationStatus(
      emailVerified: map['emailVerified'] ?? false,
      phoneVerified: map['phoneVerified'] ?? false,
      aadhaarVerified: map['aadhaarVerified'] ?? false,
      businessVerified: map['businessVerified'] ?? false,
      bankVerified: map['bankVerified'] ?? false,
      emailVerifiedAt: map['emailVerifiedAt'] != null
          ? (map['emailVerifiedAt'] as Timestamp).toDate()
          : null,
      phoneVerifiedAt: map['phoneVerifiedAt'] != null
          ? (map['phoneVerifiedAt'] as Timestamp).toDate()
          : null,
      aadhaarVerifiedAt: map['aadhaarVerifiedAt'] != null
          ? (map['aadhaarVerifiedAt'] as Timestamp).toDate()
          : null,
      businessVerifiedAt: map['businessVerifiedAt'] != null
          ? (map['businessVerifiedAt'] as Timestamp).toDate()
          : null,
      bankVerifiedAt: map['bankVerifiedAt'] != null
          ? (map['bankVerifiedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'emailVerified': emailVerified,
      'phoneVerified': phoneVerified,
      'aadhaarVerified': aadhaarVerified,
      'businessVerified': businessVerified,
      'bankVerified': bankVerified,
      'emailVerifiedAt':
          emailVerifiedAt != null ? Timestamp.fromDate(emailVerifiedAt!) : null,
      'phoneVerifiedAt':
          phoneVerifiedAt != null ? Timestamp.fromDate(phoneVerifiedAt!) : null,
      'aadhaarVerifiedAt': aadhaarVerifiedAt != null
          ? Timestamp.fromDate(aadhaarVerifiedAt!)
          : null,
      'businessVerifiedAt': businessVerifiedAt != null
          ? Timestamp.fromDate(businessVerifiedAt!)
          : null,
      'bankVerifiedAt':
          bankVerifiedAt != null ? Timestamp.fromDate(bankVerifiedAt!) : null,
    };
  }
}
