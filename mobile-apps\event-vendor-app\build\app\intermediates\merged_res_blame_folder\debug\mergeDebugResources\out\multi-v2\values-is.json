{"logs": [{"outputFile": "com.example.event_vendor_app-mergeDebugResources-51:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\225f376fd76dacfb454bf2c5d451dcf4\\transformed\\appcompat-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,2854"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,6660", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,6735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\693a1137532c792072bb392c0e3bc56d\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3013,3108,3215,3312,3412,3515,3619,6740", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3103,3210,3307,3407,3510,3614,3725,6836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d22c42c87424ac020aa74c5e9d10e64\\transformed\\browser-1.8.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5948,6139,6240,6346", "endColumns": "103,100,105,100", "endOffsets": "6047,6235,6341,6442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10147e66d4f57b3604295eae8c58c970\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4704", "endColumns": "128", "endOffsets": "4828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\50ed2967e2214bc7420465d6a51f3b13\\transformed\\preference-1.2.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,475,644,727", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "170,257,335,470,639,722,802"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5878,6052,6447,6525,6841,7010,7093", "endColumns": "69,86,77,134,168,82,79", "endOffsets": "5943,6134,6520,6655,7005,7088,7168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f2ab3d97f6976114afbee9b4807e5120\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,119", "endOffsets": "164,284"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2779,2893", "endColumns": "113,119", "endOffsets": "2888,3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b4ebdbca769560bba245800d38b3553d\\transformed\\jetified-play-services-base-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3730,3836,3990,4115,4224,4365,4490,4599,4833,4987,5093,5250,5376,5518,5672,5736,5799", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "3831,3985,4110,4219,4360,4485,4594,4699,4982,5088,5245,5371,5513,5667,5731,5794,5873"}}]}]}