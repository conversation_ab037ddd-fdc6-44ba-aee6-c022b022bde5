1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.event_vendor_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
7-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:5-74
8        android:minSdkVersion="23"
8-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:15-41
9        android:targetSdkVersion="35" />
9-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:2:42-71
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:40:5-45:15
18        <intent>
18-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:41:9-44:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:42:13-72
19-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:42:21-70
20
21            <data android:mimeType="text/plain" />
21-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
21-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:19-48
22        </intent>
23        <intent>
23-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
24            <action android:name="android.intent.action.GET_CONTENT" />
24-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
24-->[:file_picker] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
25
26            <data android:mimeType="*/*" />
26-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
26-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:19-48
27        </intent>
28    </queries>
29
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
30-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
31    <uses-permission android:name="android.permission.INTERNET" />
31-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-67
31-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-64
32    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
32-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
32-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-65
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
33-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:5-77
33-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:22-74
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
35    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:5-79
35-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:25:22-76
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:5-88
36-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:26:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:5-82
37-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:27:22-79
38    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
38-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:5-110
38-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:26:22-107
39    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
39-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\45dc9e75dd6a79254afa91cced646ac1\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
39-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\45dc9e75dd6a79254afa91cced646ac1\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
40
41    <permission
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
42        android:name="com.example.event_vendor_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="com.example.event_vendor_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
46
47    <application
48        android:name="android.app.Application"
48-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:5:9-42
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
50        android:extractNativeLibs="false"
51        android:icon="@mipmap/ic_launcher"
51-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:6:9-43
52        android:label="Event Vendor" >
52-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:4:9-37
53        <activity
53-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:7:9-28:20
54            android:name="com.example.event_vendor_app.MainActivity"
54-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:8:13-41
55            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
55-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:13:13-163
56            android:exported="true"
56-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:9:13-36
57            android:hardwareAccelerated="true"
57-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:14:13-47
58            android:launchMode="singleTop"
58-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:10:13-43
59            android:taskAffinity=""
59-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:11:13-36
60            android:theme="@style/LaunchTheme"
60-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:12:13-47
61            android:windowSoftInputMode="adjustResize" >
61-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:15:13-55
62
63            <!--
64                 Specifies an Android theme to apply to this Activity as soon as
65                 the Android process has started. This theme is visible to the user
66                 while the Flutter UI initializes. After that, this theme continues
67                 to determine the Window background behind the Flutter UI.
68            -->
69            <meta-data
69-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:20:13-23:17
70                android:name="io.flutter.embedding.android.NormalTheme"
70-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:21:15-70
71                android:resource="@style/NormalTheme" />
71-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:22:15-52
72
73            <intent-filter>
73-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:24:13-27:29
74                <action android:name="android.intent.action.MAIN" />
74-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:25:17-68
74-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:25:25-66
75
76                <category android:name="android.intent.category.LAUNCHER" />
76-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:26:17-76
76-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:26:27-74
77            </intent-filter>
78        </activity>
79        <!--
80             Don't delete the meta-data below.
81             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
82        -->
83        <meta-data
83-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:31:9-33:33
84            android:name="flutterEmbedding"
84-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:32:13-44
85            android:value="2" />
85-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:33:13-30
86
87        <service
87-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-16:19
88            android:name="com.google.firebase.components.ComponentDiscoveryService"
88-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:18-89
89            android:directBootAware="true"
89-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
90            android:exported="false" >
90-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
91            <meta-data
91-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
92-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-128
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:firebase_analytics] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-82
94            <meta-data
94-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
95                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
95-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-134
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[:cloud_firestore] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
97            <meta-data
97-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
98-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:firebase_auth] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
100            <meta-data
100-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
101-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
103            <meta-data
103-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
104                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
104-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-126
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[:firebase_storage] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
106            <meta-data
106-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
107                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
107-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[:firebase_core] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
109            <meta-data
109-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
110                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
110-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
112            <meta-data
112-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
113                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
113-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
115            <meta-data
115-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
116                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
116-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\a6e58b4f1c57789fa3a7f6fa5a0c5f5a\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
118            <meta-data
118-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
119                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
119-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
121            <meta-data
121-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
122                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
122-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
124            <meta-data
124-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
125                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
125-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
127            <meta-data
127-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
128                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
128-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\3ae8e234921c615b2ea01f739a1137fc\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
130            <meta-data
130-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:33:13-35:85
131                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
131-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:34:17-139
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.android.gms:play-services-measurement-api:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\80951ccdb00d15df090da86002adbedd\transformed\jetified-play-services-measurement-api-22.5.0\AndroidManifest.xml:35:17-82
133            <meta-data
133-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
134                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
134-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
137-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb988b2f676e8e09c4ea97c027633870\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
139            <meta-data
139-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
140                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
140-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
142            <meta-data
142-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
143                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
143-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1010a0729617405a679c956100a5f7d2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
145            <meta-data
145-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0239617ca457a1a052ba55d531e5208\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
146                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
146-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0239617ca457a1a052ba55d531e5208\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0239617ca457a1a052ba55d531e5208\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
148            <meta-data
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
149                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
151            <meta-data
151-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd5415b9ff2f9197f2700bc1acc9bb6c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
152                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
152-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd5415b9ff2f9197f2700bc1acc9bb6c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd5415b9ff2f9197f2700bc1acc9bb6c\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
154        </service>
155        <service
155-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
156            android:name="com.baseflow.geolocator.GeolocatorLocationService"
156-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
157            android:enabled="true"
157-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
158            android:exported="false"
158-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
159            android:foregroundServiceType="location" />
159-->[:geolocator_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
160        <service
160-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
161            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
161-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
162            android:exported="false"
162-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
163            android:permission="android.permission.BIND_JOB_SERVICE" />
163-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
164        <service
164-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
165            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
165-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
166            android:exported="false" >
166-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
167            <intent-filter>
167-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
168                <action android:name="com.google.firebase.MESSAGING_EVENT" />
168-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
168-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
169            </intent-filter>
170        </service>
171
172        <receiver
172-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
173            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
173-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
174            android:exported="true"
174-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
175            android:permission="com.google.android.c2dm.permission.SEND" >
175-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
176            <intent-filter>
176-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
177                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
177-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
177-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
178            </intent-filter>
179        </receiver>
180
181        <provider
181-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
182            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
182-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
183            android:authorities="com.example.event_vendor_app.flutterfirebasemessaginginitprovider"
183-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
184            android:exported="false"
184-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
185            android:initOrder="99" />
185-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
186        <provider
186-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
187            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
187-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
188            android:authorities="com.example.event_vendor_app.flutter.image_provider"
188-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
189            android:exported="false"
189-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
190            android:grantUriPermissions="true" >
190-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
191            <meta-data
191-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
192                android:name="android.support.FILE_PROVIDER_PATHS"
192-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
193                android:resource="@xml/flutter_image_picker_file_paths" />
193-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
194        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
195        <service
195-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
196            android:name="com.google.android.gms.metadata.ModuleDependencies"
196-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
197            android:enabled="false"
197-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
198            android:exported="false" >
198-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
199            <intent-filter>
199-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
200                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
200-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
200-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
201            </intent-filter>
202
203            <meta-data
203-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
204                android:name="photopicker_activity:0:required"
204-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
205                android:value="" />
205-->[:image_picker_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
206        </service>
207
208        <activity
208-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
209            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
209-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
210            android:exported="false"
210-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
211            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
211-->[:url_launcher_android] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
212        <activity
212-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
213            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
213-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
214            android:excludeFromRecents="true"
214-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
215            android:exported="true"
215-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
216            android:launchMode="singleTask"
216-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
217            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
217-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
218            <intent-filter>
218-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
219                <action android:name="android.intent.action.VIEW" />
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
219-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
220
221                <category android:name="android.intent.category.DEFAULT" />
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
221-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
222                <category android:name="android.intent.category.BROWSABLE" />
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
222-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
223
224                <data
224-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
225                    android:host="firebase.auth"
226                    android:path="/"
227                    android:scheme="genericidp" />
228            </intent-filter>
229        </activity>
230        <activity
230-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
231            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
231-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
232            android:excludeFromRecents="true"
232-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
233            android:exported="true"
233-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
234            android:launchMode="singleTask"
234-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
235            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
235-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
236            <intent-filter>
236-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
237                <action android:name="android.intent.action.VIEW" />
237-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
237-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
238
239                <category android:name="android.intent.category.DEFAULT" />
239-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
239-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
240                <category android:name="android.intent.category.BROWSABLE" />
240-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
240-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c47f50a8f55d69a62e8ebe4cd08dd82\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
241
242                <data
242-->E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\AndroidManifest.xml:43:13-50
243                    android:host="firebase.auth"
244                    android:path="/"
245                    android:scheme="recaptcha" />
246            </intent-filter>
247        </activity>
248
249        <service
249-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
250            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
250-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
251            android:enabled="true"
251-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
252            android:exported="false" >
252-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
253            <meta-data
253-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
254                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
254-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
255                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
255-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
256        </service>
257
258        <activity
258-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
259            android:name="androidx.credentials.playservices.HiddenActivity"
259-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
260            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
260-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
261            android:enabled="true"
261-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
262            android:exported="false"
262-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
263            android:fitsSystemWindows="true"
263-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
264            android:theme="@style/Theme.Hidden" >
264-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
265        </activity>
266        <activity
266-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
267            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
267-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
268            android:excludeFromRecents="true"
268-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
269            android:exported="false"
269-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
270            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
270-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
271        <!--
272            Service handling Google Sign-In user revocation. For apps that do not integrate with
273            Google Sign-In, this service will never be started.
274        -->
275        <service
275-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
276            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
276-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
277            android:exported="true"
277-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
278            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
278-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
279            android:visibleToInstantApps="true" />
279-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
280
281        <receiver
281-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
282            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
282-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
283            android:exported="true"
283-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
284            android:permission="com.google.android.c2dm.permission.SEND" >
284-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
285            <intent-filter>
285-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
286                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
286-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
286-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
287            </intent-filter>
288
289            <meta-data
289-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
290                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
290-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
291                android:value="true" />
291-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
292        </receiver>
293        <!--
294             FirebaseMessagingService performs security checks at runtime,
295             but set to not exported to explicitly avoid allowing another app to call it.
296        -->
297        <service
297-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
298            android:name="com.google.firebase.messaging.FirebaseMessagingService"
298-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
299            android:directBootAware="true"
299-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
300            android:exported="false" >
300-->[com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
301            <intent-filter android:priority="-500" >
301-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
302                <action android:name="com.google.firebase.MESSAGING_EVENT" />
302-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
302-->[:firebase_messaging] E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
303            </intent-filter>
304        </service>
305
306        <provider
306-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
307            android:name="com.google.firebase.provider.FirebaseInitProvider"
307-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
308            android:authorities="com.example.event_vendor_app.firebaseinitprovider"
308-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
309            android:directBootAware="true"
309-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
310            android:exported="false"
310-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
311            android:initOrder="100" />
311-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
312
313        <receiver
313-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:29:9-33:20
314            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
314-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:30:13-85
315            android:enabled="true"
315-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:31:13-35
316            android:exported="false" >
316-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:32:13-37
317        </receiver>
318
319        <service
319-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:35:9-38:40
320            android:name="com.google.android.gms.measurement.AppMeasurementService"
320-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:36:13-84
321            android:enabled="true"
321-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:37:13-35
322            android:exported="false" />
322-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:38:13-37
323        <service
323-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:39:9-43:72
324            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
324-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:40:13-87
325            android:enabled="true"
325-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:41:13-35
326            android:exported="false"
326-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:42:13-37
327            android:permission="android.permission.BIND_JOB_SERVICE" />
327-->[com.google.android.gms:play-services-measurement:22.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\6167970af5eabeb5f67cd00a894f0c74\transformed\jetified-play-services-measurement-22.5.0\AndroidManifest.xml:43:13-69
328
329        <uses-library
329-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
330            android:name="androidx.window.extensions"
330-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
331            android:required="false" />
331-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
332        <uses-library
332-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
333            android:name="androidx.window.sidecar"
333-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
334            android:required="false" />
334-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
335        <uses-library
335-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfd245f5c4f3b3922837e09e5cb03000\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
336            android:name="android.ext.adservices"
336-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfd245f5c4f3b3922837e09e5cb03000\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
337            android:required="false" />
337-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\bfd245f5c4f3b3922837e09e5cb03000\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
338
339        <activity
339-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cb1ac797eb912ebb8bc8e33b985a182\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
340            android:name="com.google.android.gms.common.api.GoogleApiActivity"
340-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cb1ac797eb912ebb8bc8e33b985a182\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
341            android:exported="false"
341-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cb1ac797eb912ebb8bc8e33b985a182\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
342            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
342-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5cb1ac797eb912ebb8bc8e33b985a182\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
343
344        <meta-data
344-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f57cf5e31fa23c046b6dda52e2a2733\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
345            android:name="com.google.android.gms.version"
345-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f57cf5e31fa23c046b6dda52e2a2733\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
346            android:value="@integer/google_play_services_version" />
346-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f57cf5e31fa23c046b6dda52e2a2733\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
347
348        <provider
348-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
349            android:name="androidx.startup.InitializationProvider"
349-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
350            android:authorities="com.example.event_vendor_app.androidx-startup"
350-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
351            android:exported="false" >
351-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
352            <meta-data
352-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
353                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
353-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
354                android:value="androidx.startup" />
354-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
355            <meta-data
355-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
356                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
356-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
357                android:value="androidx.startup" />
357-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
358        </provider>
359
360        <receiver
360-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
361            android:name="androidx.profileinstaller.ProfileInstallReceiver"
361-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
362            android:directBootAware="false"
362-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
363            android:enabled="true"
363-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
364            android:exported="true"
364-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
365            android:permission="android.permission.DUMP" >
365-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
366            <intent-filter>
366-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
367                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
367-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
368            </intent-filter>
369            <intent-filter>
369-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
370                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
370-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
371            </intent-filter>
372            <intent-filter>
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
373                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
374            </intent-filter>
375            <intent-filter>
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
376                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
377            </intent-filter>
378        </receiver>
379
380        <service
380-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
381            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
381-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
382            android:exported="false" >
382-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
383            <meta-data
383-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
384                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
384-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
385                android:value="cct" />
385-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\d8b186d54a4e8065c25b85e439bd7835\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
386        </service>
387        <service
387-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
388            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
388-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
389            android:exported="false"
389-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
390            android:permission="android.permission.BIND_JOB_SERVICE" >
390-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
391        </service>
392
393        <receiver
393-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
394            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
394-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
395            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
395-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\0c46f2e92f2c773e01098e9aa3f3b17d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
396        <activity
396-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
397            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
397-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
398            android:exported="false"
398-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
399            android:stateNotNeeded="true"
399-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
400            android:theme="@style/Theme.PlayCore.Transparent" />
400-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
401    </application>
402
403</manifest>
