{"logs": [{"outputFile": "com.example.event_vendor_app-mergeReleaseResources-51:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4f57cf5e31fa23c046b6dda52e2a2733\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4892", "endColumns": "176", "endOffsets": "5064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57ca061d6f83c02836e249974e533058\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6311,6495,6596,6711", "endColumns": "95,100,114,103", "endOffsets": "6402,6591,6706,6810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\584a7d9af8fd0860fd9671dd1719eb16\\transformed\\appcompat-1.1.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,895,986,1078,1173,1267,1368,1461,1556,1651,1742,1833,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,75,90,91,94,93,100,92,94,94,90,90,81,109,109,99,110,108,118,181,102,82", "endOffsets": "208,300,415,499,614,737,814,890,981,1073,1168,1262,1363,1456,1551,1646,1737,1828,1910,2020,2130,2230,2341,2450,2569,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,895,986,1078,1173,1267,1368,1461,1556,1651,1742,1833,1915,2025,2135,2235,2346,2455,2574,2756,7034", "endColumns": "107,91,114,83,114,122,76,75,90,91,94,93,100,92,94,94,90,90,81,109,109,99,110,108,118,181,102,82", "endOffsets": "208,300,415,499,614,737,814,890,981,1073,1168,1262,1363,1456,1551,1646,1737,1828,1910,2020,2130,2230,2341,2450,2569,2751,2854,7112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5cb1ac797eb912ebb8bc8e33b985a182\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3812,3923,4107,4245,4354,4522,4660,4782,5069,5239,5347,5532,5669,5841,6013,6084,6152", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "3918,4102,4240,4349,4517,4655,4777,4887,5234,5342,5527,5664,5836,6008,6079,6147,6235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3e7e3c83388001da39a5d44ececd8e9e\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3090,3187,3289,3391,3492,3595,3702,7117", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3182,3284,3386,3487,3590,3697,3807,7213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cdf9fddf924297a87ceff1db692fe5cd\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2859,2969", "endColumns": "109,120", "endOffsets": "2964,3085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0aec81ac1045261b57de152fa0160835\\transformed\\preference-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6240,6407,6815,6897,7218,7387,7466", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "6306,6490,6892,7029,7382,7461,7537"}}]}]}