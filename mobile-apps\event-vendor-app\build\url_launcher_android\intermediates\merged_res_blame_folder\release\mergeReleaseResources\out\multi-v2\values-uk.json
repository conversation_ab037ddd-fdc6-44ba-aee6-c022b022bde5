{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37be4d6ba90a05589f451f5f9eb01978\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "782,892,999,1119", "endColumns": "109,106,119,107", "endOffsets": "887,994,1114,1222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4cf7f43b1fbc1e75f08fcb2008611c3d\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,1227", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,1323"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37be4d6ba90a05589f451f5f9eb01978\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "782,892,999,1119", "endColumns": "109,106,119,107", "endOffsets": "887,994,1114,1222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4cf7f43b1fbc1e75f08fcb2008611c3d\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,1227", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,1323"}}]}]}