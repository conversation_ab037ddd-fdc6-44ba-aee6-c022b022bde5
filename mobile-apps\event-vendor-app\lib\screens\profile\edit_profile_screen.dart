import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../services/vendor_profile_service.dart';
import '../../config/app_theme.dart';
import '../../models/vendor_model.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final VendorProfileService _profileService = VendorProfileService();

  // Form controllers
  final _businessNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _websiteController = TextEditingController();

  // Business details controllers
  final _gstController = TextEditingController();
  final _panController = TextEditingController();
  final _businessTypeController = TextEditingController();
  final _establishedYearController = TextEditingController();
  final _employeeCountController = TextEditingController();
  final _annualRevenueController = TextEditingController();

  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCurrentData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _businessNameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _websiteController.dispose();
    _gstController.dispose();
    _panController.dispose();
    _businessTypeController.dispose();
    _establishedYearController.dispose();
    _employeeCountController.dispose();
    _annualRevenueController.dispose();
    super.dispose();
  }

  void _loadCurrentData() {
    final authProvider =
        Provider.of<VendorAuthProvider>(context, listen: false);
    final vendor = authProvider.currentVendor;

    if (vendor != null) {
      // Load profile data
      _businessNameController.text = vendor.profile.businessName;
      _descriptionController.text = vendor.profile.description;
      _addressController.text = vendor.profile.address;
      _cityController.text = vendor.profile.city;
      _stateController.text = vendor.profile.state;
      _pincodeController.text = vendor.profile.pincode;
      _websiteController.text = vendor.profile.website ?? '';

      // Load business details
      _gstController.text = vendor.businessDetails.gstNumber ?? '';
      _panController.text = vendor.businessDetails.panNumber ?? '';
      _businessTypeController.text = vendor.businessDetails.businessType;
      _establishedYearController.text =
          vendor.businessDetails.establishedYear.toString();
      _employeeCountController.text =
          vendor.businessDetails.employeeCount.toString();
      _annualRevenueController.text =
          vendor.businessDetails.annualRevenue.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Basic Info'),
            Tab(text: 'Business'),
            Tab(text: 'Services'),
            Tab(text: 'Portfolio'),
          ],
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveProfile,
            ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBasicInfoTab(),
          _buildBusinessTab(),
          _buildServicesTab(),
          _buildPortfolioTab(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.error, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red.shade600),
                    ),
                  ),
                ],
              ),
            ),
          const Text(
            'Business Information',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _businessNameController,
            decoration: const InputDecoration(
              labelText: 'Business Name *',
              border: OutlineInputBorder(),
              hintText: 'Enter your business name',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Business Description *',
              border: OutlineInputBorder(),
              hintText: 'Describe your business and services',
            ),
            maxLines: 4,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _websiteController,
            decoration: const InputDecoration(
              labelText: 'Website',
              border: OutlineInputBorder(),
              hintText: 'https://yourwebsite.com',
            ),
            keyboardType: TextInputType.url,
          ),
          const SizedBox(height: 24),
          const Text(
            'Address Information',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _addressController,
            decoration: const InputDecoration(
              labelText: 'Full Address *',
              border: OutlineInputBorder(),
              hintText: 'Enter complete address',
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City *',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _stateController,
                  decoration: const InputDecoration(
                    labelText: 'State *',
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _pincodeController,
            decoration: const InputDecoration(
              labelText: 'Pincode *',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveBasicInfo,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Save Basic Information'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Business Registration Details',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _gstController,
            decoration: const InputDecoration(
              labelText: 'GST Number',
              border: OutlineInputBorder(),
              hintText: 'Enter GST registration number',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _panController,
            decoration: const InputDecoration(
              labelText: 'PAN Number',
              border: OutlineInputBorder(),
              hintText: 'Enter PAN number',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _businessTypeController,
            decoration: const InputDecoration(
              labelText: 'Business Type',
              border: OutlineInputBorder(),
              hintText: 'e.g., Private Limited, Partnership',
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _establishedYearController,
                  decoration: const InputDecoration(
                    labelText: 'Established Year',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _employeeCountController,
                  decoration: const InputDecoration(
                    labelText: 'Employee Count',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _annualRevenueController,
            decoration: const InputDecoration(
              labelText: 'Annual Revenue (₹)',
              border: OutlineInputBorder(),
              hintText: 'Enter annual revenue',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveBusinessDetails,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text('Save Business Details'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServicesTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.construction, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Services Management',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Coming Soon!',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPortfolioTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.photo_library, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Portfolio Management',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Coming Soon!',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Future<void> _saveBasicInfo() async {
    if (_businessNameController.text.isEmpty ||
        _descriptionController.text.isEmpty ||
        _addressController.text.isEmpty ||
        _cityController.text.isEmpty ||
        _stateController.text.isEmpty ||
        _pincodeController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Please fill all required fields';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider =
          Provider.of<VendorAuthProvider>(context, listen: false);
      final vendor = authProvider.currentVendor!;

      final updatedProfile = vendor.profile.copyWith(
        businessName: _businessNameController.text,
        description: _descriptionController.text,
        address: _addressController.text,
        city: _cityController.text,
        state: _stateController.text,
        pincode: _pincodeController.text,
        website: _websiteController.text,
      );

      final success = await _profileService.updateVendorProfile(
        vendorId: vendor.uid,
        profile: updatedProfile,
      );

      if (success) {
        // Update local provider
        await authProvider.refreshCurrentVendor();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Basic information saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        setState(() {
          _errorMessage = 'Failed to save basic information';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveBusinessDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authProvider =
          Provider.of<VendorAuthProvider>(context, listen: false);
      final vendor = authProvider.currentVendor!;

      final updatedBusinessDetails = vendor.businessDetails.copyWith(
        gstNumber: _gstController.text,
        panNumber: _panController.text,
        businessType: _businessTypeController.text,
        establishedYear: int.tryParse(_establishedYearController.text) ?? 0,
        employeeCount: int.tryParse(_employeeCountController.text) ?? 0,
        annualRevenue: double.tryParse(_annualRevenueController.text) ?? 0.0,
      );

      final success = await _profileService.updateBusinessDetails(
        vendorId: vendor.uid,
        businessDetails: updatedBusinessDetails,
      );

      if (success) {
        // Update local provider
        await authProvider.refreshCurrentVendor();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Business details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        setState(() {
          _errorMessage = 'Failed to save business details';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveProfile() async {
    await _saveBasicInfo();
    if (_errorMessage == null) {
      await _saveBusinessDetails();
    }
  }
}
