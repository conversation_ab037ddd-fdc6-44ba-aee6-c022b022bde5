import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_auth_provider.dart';
import '../../services/team_member_auth_service.dart';
import '../../services/task_management_service.dart';
import '../../config/app_theme.dart';
import '../../models/task_model.dart';
import '../../models/team_member_model.dart';

class TasksScreen extends StatefulWidget {
  const TasksScreen({super.key});

  @override
  State<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends State<TasksScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TaskManagementService _taskService = TaskManagementService();
  final TeamMemberAuthService _teamMemberService = TeamMemberAuthService();

  List<TaskModel> _tasks = [];
  List<TeamMemberModel> _teamMembers = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadRealData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRealData() async {
    final authProvider =
        Provider.of<VendorAuthProvider>(context, listen: false);
    final vendor = authProvider.currentVendor;

    if (vendor == null) {
      setState(() {
        _errorMessage = 'No vendor found';
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Load team members and tasks from Firebase
      final teamMembers =
          await _teamMemberService.getTeamMembersByVendor(vendor.uid);
      final tasks = await _taskService.getTasksForVendor(vendor.uid);

      if (kDebugMode) {
        print('🔍 Tasks loaded for vendor ${vendor.uid}:');
        print('   Vendor Name: ${vendor.profile.name}');
        print('   Team members: ${teamMembers.length}');
        print('   Tasks: ${tasks.length}');
        for (var task in tasks) {
          print(
              '   - ${task.title} (${task.status}) [VendorID: ${task.vendorId}]');
        }
        // Verify data isolation
        final hasWrongVendorData =
            tasks.any((task) => task.vendorId != vendor.uid) ||
                teamMembers.any((member) => member.vendorId != vendor.uid);
        if (hasWrongVendorData) {
          print('🚨 WARNING: Found data from other vendors!');
        } else {
          print(
              '✅ Data isolation verified - all data belongs to current vendor');
        }
      }

      setState(() {
        _teamMembers = teamMembers;
        _tasks = tasks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  List<TaskModel> _getTasksByStatus(TaskStatus status) {
    return _tasks.where((task) => task.status == status).toList();
  }

  Future<void> _refreshData() async {
    await _loadRealData();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<VendorAuthProvider>(context);
    final vendor = authProvider.currentVendor;

    if (vendor == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Tasks'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading tasks and team members...'),
            ],
          ),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Tasks'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red.shade400),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _refreshData,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tasks'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.add_task),
              onPressed: _createSampleTasks,
              tooltip: 'Create Sample Tasks',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddTaskDialog(),
            tooltip: 'Add Task',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          labelPadding: const EdgeInsets.symmetric(horizontal: 16),
          tabs: [
            Tab(text: 'All (${_tasks.length})'),
            Tab(
                text:
                    'Pending (${_getTasksByStatus(TaskStatus.pending).length})'),
            Tab(
                text:
                    'In Progress (${_getTasksByStatus(TaskStatus.inProgress).length})'),
            Tab(
                text:
                    'Completed (${_getTasksByStatus(TaskStatus.completed).length})'),
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildTaskList(_tasks),
            _buildTaskList(_getTasksByStatus(TaskStatus.pending)),
            _buildTaskList(_getTasksByStatus(TaskStatus.inProgress)),
            _buildTaskList(_getTasksByStatus(TaskStatus.completed)),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddTaskDialog(),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        tooltip: 'Add New Task',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTaskList(List<TaskModel> tasks) {
    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.task_alt, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              'No tasks found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              _teamMembers.isEmpty
                  ? 'Add team members first, then create tasks'
                  : 'Create your first task to get started',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (_teamMembers.isEmpty)
              ElevatedButton.icon(
                onPressed: () => _showAddTeamMemberDialog(),
                icon: const Icon(Icons.person_add),
                label: const Text('Add Team Member'),
              )
            else ...[
              ElevatedButton.icon(
                onPressed: () => _showAddTaskDialog(),
                icon: const Icon(Icons.add_task),
                label: const Text('Create Task'),
              ),
              if (kDebugMode) ...[
                const SizedBox(height: 8),
                OutlinedButton.icon(
                  onPressed: _createSampleTasks,
                  icon: const Icon(Icons.auto_awesome),
                  label: const Text('Create Sample Tasks'),
                ),
              ],
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskCard(task);
      },
    );
  }

  Widget _buildTaskCard(TaskModel task) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: () => _showTaskDetails(task),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.title,
                      style: AppTheme.headingSmall.copyWith(
                        decoration: task.status == TaskStatus.completed
                            ? TextDecoration.lineThrough
                            : null,
                      ),
                    ),
                  ),
                  _buildPriorityChip(task.priority),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                task.description,
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.grey.shade600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildStatusChip(task.status),
                  const SizedBox(width: 8),
                  _buildCategoryChip(task.category),
                  const Spacer(),
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: _getDueDateColor(task.dueDate),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDueDate(task.dueDate),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getDueDateColor(task.dueDate),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(TaskStatus status) {
    Color color;
    String label;
    IconData icon;

    switch (status) {
      case TaskStatus.pending:
        color = Colors.orange;
        label = 'Pending';
        icon = Icons.pending;
        break;
      case TaskStatus.inProgress:
        color = Colors.blue;
        label = 'In Progress';
        icon = Icons.work;
        break;
      case TaskStatus.completed:
        color = Colors.green;
        label = 'Completed';
        icon = Icons.check_circle;
        break;
      case TaskStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
        icon = Icons.cancel;
        break;
      case TaskStatus.onHold:
        color = Colors.grey;
        label = 'On Hold';
        icon = Icons.pause;
        break;
    }

    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildPriorityChip(TaskPriority priority) {
    Color color;
    String label;

    switch (priority) {
      case TaskPriority.low:
        color = Colors.green;
        label = 'Low';
        break;
      case TaskPriority.medium:
        color = Colors.orange;
        label = 'Medium';
        break;
      case TaskPriority.high:
        color = Colors.red;
        label = 'High';
        break;
      case TaskPriority.urgent:
        color = Colors.purple;
        label = 'Urgent';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCategoryChip(TaskCategory category) {
    String label;
    IconData icon;

    switch (category) {
      case TaskCategory.general:
        label = 'General';
        icon = Icons.task;
        break;
      case TaskCategory.setup:
        label = 'Setup';
        icon = Icons.settings;
        break;
      case TaskCategory.event:
        label = 'Event';
        icon = Icons.event;
        break;
      case TaskCategory.maintenance:
        label = 'Maintenance';
        icon = Icons.build;
        break;
      case TaskCategory.documentation:
        label = 'Documentation';
        icon = Icons.description;
        break;
      case TaskCategory.training:
        label = 'Training';
        icon = Icons.school;
        break;
      case TaskCategory.client:
        label = 'Client';
        icon = Icons.people;
        break;
      case TaskCategory.technical:
        label = 'Technical';
        icon = Icons.computer;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: Colors.grey.shade600),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDueDateColor(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) return Colors.red; // Overdue
    if (difference == 0) return Colors.orange; // Due today
    if (difference <= 2) return Colors.amber; // Due soon
    return Colors.grey.shade600; // Normal
  }

  String _formatDueDate(DateTime dueDate) {
    final now = DateTime.now();
    final difference = dueDate.difference(now).inDays;

    if (difference < 0) return 'Overdue';
    if (difference == 0) return 'Due today';
    if (difference == 1) return 'Due tomorrow';
    return 'Due in $difference days';
  }

  void _showTaskDetails(TaskModel task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(task.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(task.description),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('Status: ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                _buildStatusChip(task.status),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Priority: ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                _buildPriorityChip(task.priority),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Text('Category: ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                _buildCategoryChip(task.category),
              ],
            ),
            const SizedBox(height: 8),
            Text('Due: ${_formatDueDate(task.dueDate)}'),
            if (task.completedAt != null) ...[
              const SizedBox(height: 8),
              Text('Completed: ${task.completedAt!.toString().split(' ')[0]}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (task.status != TaskStatus.completed)
            ElevatedButton(
              onPressed: () async {
                final newStatus = task.status == TaskStatus.pending
                    ? TaskStatus.inProgress
                    : TaskStatus.completed;

                Navigator.of(context).pop();

                // Show loading
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Updating task...')),
                );

                try {
                  final authProvider =
                      Provider.of<VendorAuthProvider>(context, listen: false);
                  final vendor = authProvider.currentVendor;

                  if (vendor == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Error: No vendor found')),
                    );
                    return;
                  }

                  // Use updateTaskProgress to change status
                  final newPercentage = newStatus == TaskStatus.completed
                      ? 100.0
                      : newStatus == TaskStatus.inProgress
                          ? 50.0
                          : 0.0;

                  final success = await _taskService.updateTaskProgress(
                    taskId: task.id,
                    newPercentage: newPercentage,
                    updatedBy: vendor.uid,
                    message:
                        'Status updated to ${newStatus.toString().split('.').last}',
                  );

                  if (success) {
                    // Refresh the task list
                    await _loadRealData();

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Task updated successfully!')),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Failed to update task')),
                    );
                  }
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error updating task: $e')),
                  );
                }
              },
              child: Text(
                  task.status == TaskStatus.pending ? 'Start' : 'Complete'),
            ),
        ],
      ),
    );
  }

  void _showAddTaskDialog() {
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    TaskPriority selectedPriority = TaskPriority.medium;
    TaskCategory selectedCategory = TaskCategory.general;
    DateTime selectedDate = DateTime.now().add(const Duration(days: 1));

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Dialog(
          insetPadding: const EdgeInsets.all(20),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: 400,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  decoration: const BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Create New Task',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Task Title
                        const Text(
                          'Task Title *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: titleController,
                          decoration: InputDecoration(
                            hintText: 'Enter task title',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Description
                        const Text(
                          'Description *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: descriptionController,
                          decoration: InputDecoration(
                            hintText: 'Enter task description',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          maxLines: 3,
                        ),
                        const SizedBox(height: 16),
                        // Priority and Category Row
                        Row(
                          children: [
                            // Priority
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Priority',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  DropdownButtonFormField<TaskPriority>(
                                    value: selectedPriority,
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                    ),
                                    items: TaskPriority.values.map((priority) {
                                      return DropdownMenuItem(
                                        value: priority,
                                        child: Text(
                                          priority.name.toUpperCase(),
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setDialogState(() {
                                        selectedPriority = value!;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            // Category
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Category',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  DropdownButtonFormField<TaskCategory>(
                                    value: selectedCategory,
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                    ),
                                    items: TaskCategory.values.map((category) {
                                      return DropdownMenuItem(
                                        value: category,
                                        child: Text(
                                          category.name.toUpperCase(),
                                          style: const TextStyle(fontSize: 14),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setDialogState(() {
                                        selectedCategory = value!;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // Due Date
                        const Text(
                          'Due Date',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () async {
                            final date = await showDatePicker(
                              context: context,
                              initialDate: selectedDate,
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );
                            if (date != null) {
                              setDialogState(() {
                                selectedDate = date;
                              });
                            }
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.calendar_today,
                                  color: AppTheme.primaryColor,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Actions
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Cancel',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 2,
                          ),
                          onPressed: () async {
                            if (titleController.text.isNotEmpty) {
                              final authProvider =
                                  Provider.of<VendorAuthProvider>(context,
                                      listen: false);
                              final vendor = authProvider.currentVendor;

                              if (vendor == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                      content: Text('Error: No vendor found')),
                                );
                                return;
                              }

                              Navigator.of(context).pop();

                              // Show loading
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text('Creating task...')),
                              );

                              try {
                                final createdTask =
                                    await _taskService.createTask(
                                  vendorId: vendor.uid,
                                  title: titleController.text,
                                  description: descriptionController.text,
                                  priority: selectedPriority,
                                  category: selectedCategory,
                                  dueDate: selectedDate,
                                  assignedToId: vendor.uid, // Self-assigned
                                  assignedToName: vendor.profile.name,
                                  assignedBy: vendor.uid,
                                  estimatedHours: 1.0,
                                );

                                if (createdTask != null) {
                                  // Refresh the task list
                                  await _loadRealData();

                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content:
                                            Text('Task created successfully!')),
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('Failed to create task')),
                                  );
                                }
                              } catch (e) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                      content: Text('Error creating task: $e')),
                                );
                              }
                            }
                          },
                          child: const Text(
                            'Create Task',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showAddTeamMemberDialog() {
    // TODO: Implement add team member dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add team member feature coming soon!'),
      ),
    );
  }

  // Debug method to create sample tasks
  Future<void> _createSampleTasks() async {
    final authProvider =
        Provider.of<VendorAuthProvider>(context, listen: false);
    final vendor = authProvider.currentVendor;

    if (vendor == null) return;

    final sampleTasks = [
      {
        'title': 'Complete Profile Setup',
        'description': 'Fill in all business details and upload documents',
        'priority': TaskPriority.high,
        'category': TaskCategory.setup,
      },
      {
        'title': 'Upload Portfolio Images',
        'description': 'Add photos of previous events and work samples',
        'priority': TaskPriority.medium,
        'category': TaskCategory.documentation,
      },
      {
        'title': 'Review Event Contract',
        'description': 'Check terms and conditions for upcoming wedding event',
        'priority': TaskPriority.urgent,
        'category': TaskCategory.client,
      },
    ];

    for (var taskData in sampleTasks) {
      try {
        await _taskService.createTask(
          vendorId: vendor.uid,
          title: taskData['title'] as String,
          description: taskData['description'] as String,
          priority: taskData['priority'] as TaskPriority,
          category: taskData['category'] as TaskCategory,
          dueDate: DateTime.now().add(const Duration(days: 7)),
          assignedToId: vendor.uid,
          assignedToName: vendor.profile.name,
          assignedBy: vendor.uid,
          estimatedHours: 2.0,
        );
      } catch (e) {
        if (kDebugMode) {
          print('Error creating sample task: $e');
        }
      }
    }

    // Refresh the task list
    await _loadRealData();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sample tasks created!')),
    );
  }
}

// Using TaskModel, TaskStatus, TaskPriority, and TaskCategory from task_model.dart
