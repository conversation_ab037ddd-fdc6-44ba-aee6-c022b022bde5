import 'package:flutter/material.dart';
import '../services/aadhar_verification_service.dart';

/// Test screen for Aadhar API integration
class TestAadharScreen extends StatefulWidget {
  const TestAadharScreen({super.key});

  @override
  State<TestAadharScreen> createState() => _TestAadharScreenState();
}

class _TestAadharScreenState extends State<TestAadharScreen> {
  final _aadharController = TextEditingController();
  final _otpController = TextEditingController();
  final _aadharService = AadharVerificationService();

  String? _transactionId;
  bool _isLoading = false;
  String _result = '';

  @override
  void dispose() {
    _aadharController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  Future<void> _testCredentials() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing API credentials...';
    });

    try {
      final result = await _aadharService.testAPICredentials();

      setState(() {
        _result = 'Credentials Test Result:\n${_formatResult(result)}';
      });
    } catch (e) {
      setState(() {
        _result = 'Error testing credentials: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateOTP() async {
    if (_aadharController.text.isEmpty) {
      setState(() {
        _result = 'Please enter Aadhar number';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Generating OTP...';
    });

    try {
      final result =
          await _aadharService.generateAadharOtp(_aadharController.text);

      if (result['success']) {
        _transactionId = result['data']['transactionId'];
        setState(() {
          _result = 'OTP Generated Successfully!\n${_formatResult(result)}';
        });
      } else {
        setState(() {
          _result = 'OTP Generation Failed:\n${_formatResult(result)}';
        });
      }
    } catch (e) {
      setState(() {
        _result = 'Error generating OTP: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _verifyOTP() async {
    if (_otpController.text.isEmpty || _transactionId == null) {
      setState(() {
        _result = 'Please generate OTP first and enter the received OTP';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _result = 'Verifying OTP...';
    });

    try {
      final result = await _aadharService.verifyAadharOtp(
          _transactionId!, _otpController.text);

      setState(() {
        _result = 'OTP Verification Result:\n${_formatResult(result)}';
      });
    } catch (e) {
      setState(() {
        _result = 'Error verifying OTP: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatResult(Map<String, dynamic> result) {
    final buffer = StringBuffer();
    buffer.writeln('Success: ${result['success']}');
    if (result['error'] != null) {
      buffer.writeln('Error: ${result['error']}');
    }
    if (result['data'] != null) {
      buffer.writeln('Data: ${result['data']}');
    }
    if (result['details'] != null) {
      buffer.writeln('Details: ${result['details']}');
    }
    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Aadhar API'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Test Credentials
            ElevatedButton(
              onPressed: _isLoading ? null : _testCredentials,
              child: const Text('Test API Credentials'),
            ),

            const SizedBox(height: 16),

            // Aadhar Input
            TextField(
              controller: _aadharController,
              decoration: const InputDecoration(
                labelText: 'Aadhar Number (12 digits)',
                border: OutlineInputBorder(),
                hintText: '123456789012',
              ),
              keyboardType: TextInputType.number,
              maxLength: 12,
            ),

            const SizedBox(height: 16),

            // Generate OTP
            ElevatedButton(
              onPressed: _isLoading ? null : _generateOTP,
              child: const Text('Generate OTP'),
            ),

            const SizedBox(height: 16),

            // OTP Input
            TextField(
              controller: _otpController,
              decoration: const InputDecoration(
                labelText: 'OTP (6 digits)',
                border: OutlineInputBorder(),
                hintText: '123456',
              ),
              keyboardType: TextInputType.number,
              maxLength: 6,
            ),

            const SizedBox(height: 16),

            // Verify OTP
            ElevatedButton(
              onPressed: _isLoading ? null : _verifyOTP,
              child: const Text('Verify OTP'),
            ),

            const SizedBox(height: 16),

            // Loading Indicator
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),

            const SizedBox(height: 16),

            // Result Display
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Result:',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _result.isEmpty ? 'No results yet' : _result,
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Instructions
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Instructions:',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                        '1. Select service type (Direct for mobile, Proxy for web)'),
                    const Text('2. Test API credentials first'),
                    const Text('3. Enter a valid 12-digit Aadhar number'),
                    const Text(
                        '4. Generate OTP (will be sent to registered mobile)'),
                    const Text('5. Enter received OTP and verify'),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
