com.example.event_vendor_app-jetified-savedstate-1.2.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\046ba1c904796709f73eecd5feb628f4\transformed\jetified-savedstate-1.2.1\res
com.example.event_vendor_app-preference-1.2.1-1 C:\Users\<USER>\.gradle\caches\transforms-3\0aec81ac1045261b57de152fa0160835\transformed\preference-1.2.1\res
com.example.event_vendor_app-jetified-core-1.0.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\0e357cb7fb15ebff6cd128c526b18c33\transformed\jetified-core-1.0.0\res
com.example.event_vendor_app-lifecycle-viewmodel-2.7.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\0fd56fbbc92a4aa22ad7e0e5df8c3dc5\transformed\lifecycle-viewmodel-2.7.0\res
com.example.event_vendor_app-jetified-ads-adservices-java-1.1.0-beta11-4 C:\Users\<USER>\.gradle\caches\transforms-3\12c78ccdb72b3c58abfb16fd37122cef\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.example.event_vendor_app-jetified-lifecycle-runtime-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\15caf625181f688b605d0b0f582fc7ee\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.event_vendor_app-jetified-appcompat-resources-1.1.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\1d93be0c4f4c9012a809c661248324a0\transformed\jetified-appcompat-resources-1.1.0\res
com.example.event_vendor_app-jetified-activity-1.10.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\1e02478319b0ce858b690d0837de1709\transformed\jetified-activity-1.10.1\res
com.example.event_vendor_app-jetified-datastore-release-8 C:\Users\<USER>\.gradle\caches\transforms-3\216417ecc90f95cf5d6737c726deb0ac\transformed\jetified-datastore-release\res
com.example.event_vendor_app-jetified-credentials-play-services-auth-1.2.0-rc01-9 C:\Users\<USER>\.gradle\caches\transforms-3\260d7c99ac7a64c8a2e28e6751d912b3\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.event_vendor_app-jetified-firebase-common-21.0.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\28eb157974d8d000caaea7dc5d98750c\transformed\jetified-firebase-common-21.0.0\res
com.example.event_vendor_app-jetified-datastore-core-release-11 C:\Users\<USER>\.gradle\caches\transforms-3\337d71935036f47f9bb4d838cbdb1d2f\transformed\jetified-datastore-core-release\res
com.example.event_vendor_app-coordinatorlayout-1.0.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\3a56d197344080e419b793ccc3ea6c80\transformed\coordinatorlayout-1.0.0\res
com.example.event_vendor_app-core-1.13.1-13 C:\Users\<USER>\.gradle\caches\transforms-3\3e7e3c83388001da39a5d44ececd8e9e\transformed\core-1.13.1\res
com.example.event_vendor_app-jetified-play-services-auth-21.0.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\4371d7a4116c7ece602da711ecb35109\transformed\jetified-play-services-auth-21.0.0\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\4987cd2e1beea703829338af6b4e2de8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.event_vendor_app-jetified-play-services-basement-18.5.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\4f57cf5e31fa23c046b6dda52e2a2733\transformed\jetified-play-services-basement-18.5.0\res
com.example.event_vendor_app-jetified-firebase-messaging-24.1.2-17 C:\Users\<USER>\.gradle\caches\transforms-3\53c4f9c69704b9416f8b1e0a1780a471\transformed\jetified-firebase-messaging-24.1.2\res
com.example.event_vendor_app-core-runtime-2.2.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\54da33a6c28bad795c178532eb01fcbf\transformed\core-runtime-2.2.0\res
com.example.event_vendor_app-browser-1.8.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\57ca061d6f83c02836e249974e533058\transformed\browser-1.8.0\res
com.example.event_vendor_app-appcompat-1.1.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\584a7d9af8fd0860fd9671dd1719eb16\transformed\appcompat-1.1.0\res
com.example.event_vendor_app-jetified-profileinstaller-1.4.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\59284aa7dde8ef793494a31e827f97be\transformed\jetified-profileinstaller-1.4.0\res
com.example.event_vendor_app-jetified-lifecycle-livedata-core-ktx-2.7.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\5ad80befc30cc722d880eecf57a2ff65\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.event_vendor_app-jetified-play-services-base-18.5.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\5cb1ac797eb912ebb8bc8e33b985a182\transformed\jetified-play-services-base-18.5.0\res
com.example.event_vendor_app-jetified-core-common-2.0.3-24 C:\Users\<USER>\.gradle\caches\transforms-3\61757001ead96860f23d75d30fd2ed2c\transformed\jetified-core-common-2.0.3\res
com.example.event_vendor_app-fragment-1.7.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\641ab33f69bb12a33b5460068aad9382\transformed\fragment-1.7.1\res
com.example.event_vendor_app-jetified-core-ktx-1.13.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\6839cd751f1f71447f65cc549b661e36\transformed\jetified-core-ktx-1.13.1\res
com.example.event_vendor_app-jetified-lifecycle-viewmodel-ktx-2.7.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\6d7eadf4c04564f9de5b7929043d02aa\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.event_vendor_app-jetified-lifecycle-process-2.7.0-28 C:\Users\<USER>\.gradle\caches\transforms-3\8392d77bfd9488b1f3382d53b788af15\transformed\jetified-lifecycle-process-2.7.0\res
com.example.event_vendor_app-jetified-annotation-experimental-1.4.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\87db32e2439fce478b8839255c2f0246\transformed\jetified-annotation-experimental-1.4.0\res
com.example.event_vendor_app-lifecycle-runtime-2.7.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\8a1cfa91b80fdc366f8f9b7b1b771cf2\transformed\lifecycle-runtime-2.7.0\res
com.example.event_vendor_app-jetified-startup-runtime-1.1.1-31 C:\Users\<USER>\.gradle\caches\transforms-3\8a2d9558fbb56f4071cb46b712479c80\transformed\jetified-startup-runtime-1.1.1\res
com.example.event_vendor_app-jetified-savedstate-ktx-1.2.1-32 C:\Users\<USER>\.gradle\caches\transforms-3\a57b6fed72e30b8ad662c51f04032e4e\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.event_vendor_app-localbroadcastmanager-1.1.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\a5f4d36612efa56108cf02a360bc4f0d\transformed\localbroadcastmanager-1.1.0\res
com.example.event_vendor_app-jetified-activity-ktx-1.10.1-34 C:\Users\<USER>\.gradle\caches\transforms-3\aeb5b15299fe464bd07903965dcc04c1\transformed\jetified-activity-ktx-1.10.1\res
com.example.event_vendor_app-recyclerview-1.0.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\b289f56be5257f2324d34e69e30cc692\transformed\recyclerview-1.0.0\res
com.example.event_vendor_app-jetified-ads-adservices-1.1.0-beta11-36 C:\Users\<USER>\.gradle\caches\transforms-3\bfd245f5c4f3b3922837e09e5cb03000\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.example.event_vendor_app-jetified-window-1.2.0-37 C:\Users\<USER>\.gradle\caches\transforms-3\c3392fa676a925dd7c068329e3015472\transformed\jetified-window-1.2.0\res
com.example.event_vendor_app-jetified-credentials-1.2.0-rc01-38 C:\Users\<USER>\.gradle\caches\transforms-3\cdf9fddf924297a87ceff1db692fe5cd\transformed\jetified-credentials-1.2.0-rc01\res
com.example.event_vendor_app-jetified-tracing-1.2.0-39 C:\Users\<USER>\.gradle\caches\transforms-3\d09e693c49c1569f91c69f3ae320d8f1\transformed\jetified-tracing-1.2.0\res
com.example.event_vendor_app-lifecycle-livedata-core-2.7.0-40 C:\Users\<USER>\.gradle\caches\transforms-3\d2af524fff83f2676a1167277869fa8e\transformed\lifecycle-livedata-core-2.7.0\res
com.example.event_vendor_app-jetified-fragment-ktx-1.7.1-41 C:\Users\<USER>\.gradle\caches\transforms-3\d2e0c1d2d699a83a2f91cc307fd5ec47\transformed\jetified-fragment-ktx-1.7.1\res
com.example.event_vendor_app-jetified-window-java-1.2.0-42 C:\Users\<USER>\.gradle\caches\transforms-3\d761947bd5978c96dc7d9087dcea15f7\transformed\jetified-window-java-1.2.0\res
com.example.event_vendor_app-transition-1.4.1-43 C:\Users\<USER>\.gradle\caches\transforms-3\f07f343c356e342fc6826481ac1cc2e4\transformed\transition-1.4.1\res
com.example.event_vendor_app-jetified-core-viewtree-1.0.0-44 C:\Users\<USER>\.gradle\caches\transforms-3\f7d69835676e60d0ecc95cdcaf025c90\transformed\jetified-core-viewtree-1.0.0\res
com.example.event_vendor_app-slidingpanelayout-1.2.0-45 C:\Users\<USER>\.gradle\caches\transforms-3\f957f910da7d903b6101965e45f78431\transformed\slidingpanelayout-1.2.0\res
com.example.event_vendor_app-jetified-datastore-preferences-release-46 C:\Users\<USER>\.gradle\caches\transforms-3\f9b8fe91cfd6df3f7bbaad9f41d057e0\transformed\jetified-datastore-preferences-release\res
com.example.event_vendor_app-main-47 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\main\res
com.example.event_vendor_app-release-48 E:\Ongoing\lib\mobile-apps\event-vendor-app\android\app\src\release\res
com.example.event_vendor_app-pngs-49 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\pngs\release
com.example.event_vendor_app-resValues-50 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\generated\res\resValues\release
com.example.event_vendor_app-packageReleaseResources-51 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\release\packageReleaseResources\merged.dir
com.example.event_vendor_app-packageReleaseResources-52 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.example.event_vendor_app-merged-not-compiled-resources-53 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged-not-compiled-resources\release
com.example.event_vendor_app-release-54 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\app\intermediates\merged_res\release\mergeReleaseResources
com.example.event_vendor_app-release-55 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\cloud_firestore\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-56 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\connectivity_plus\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-57 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\file_picker\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-58 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_analytics\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-59 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_auth\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-60 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_core\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-61 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_messaging\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-62 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\firebase_storage\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-63 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-64 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geocoding_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-65 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\geolocator_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-66 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\google_sign_in_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-67 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\image_picker_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-68 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\package_info_plus\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-69 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\path_provider_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-70 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\permission_handler_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-71 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\shared_preferences_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-72 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\sqflite_android\intermediates\packaged_res\release\packageReleaseResources
com.example.event_vendor_app-release-73 E:\Ongoing\lib\mobile-apps\event-vendor-app\build\url_launcher_android\intermediates\packaged_res\release\packageReleaseResources
