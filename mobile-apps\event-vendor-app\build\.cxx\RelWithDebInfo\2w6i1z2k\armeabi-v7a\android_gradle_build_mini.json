{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\.cxx\\RelWithDebInfo\\2w6i1z2k\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Ongoing\\lib\\mobile-apps\\event-vendor-app\\build\\.cxx\\RelWithDebInfo\\2w6i1z2k\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}